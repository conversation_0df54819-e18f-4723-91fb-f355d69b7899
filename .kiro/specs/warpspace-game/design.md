# Design Document

## Overview

WarpSpace is a browser-based vertical scrolling shooter built with HTML5 Canvas and JavaScript, featuring a unique reality-warping mechanic powered by AI-generated content. The game integrates with Orange ID for authentication, Orange SDK for progress tracking, and uses Fal.ai for dynamic battlefield generation. The core gameplay loop revolves around a deflationary WISH token economy that drives strategic decision-making through reality warps.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Game Client HTML5] --> B[Game Engine Core]
    B --> C[Authentication Layer]
    B --> D[Game State Manager]
    B --> E[Rendering Engine]
    B --> F[AI Integration Layer]
    
    C --> G[Orange ID Service]
    D --> H[Orange SDK]
    F --> I[Fal.ai API]
    F --> J[LLM Analysis Service]
    
    D --> K[Token Economy Manager]
    D --> L[Level Manager]
    D --> M[Enemy System]
    D --> N[Reality Warp System]
    
    K --> O[Raffle System]
    L --> P[Genie Interface]
    N --> Q[Environment Generator]
```

### Technology Stack

- **Frontend**: HTML5 Canvas, JavaScript ES6+, CSS3
- **Authentication**: Orange ID (Bedrock Passport)
- **Data Persistence**: Orange SDK (Goama)
- **AI Services**: Fal.ai (image generation), LLM API (environment analysis)
- **Build Tools**: Vite for development and bundling
- **Deployment**: Static hosting compatible with iframe embedding

## Components and Interfaces

### 1. Game Engine Core

**Purpose**: Central game loop and state management

**Key Classes**:
```javascript
class GameEngine {
  constructor()
  init()
  update(deltaTime)
  render()
  pause()
  resume()
  destroy()
}

class GameState {
  currentLevel: number
  playerStats: PlayerStats
  wishTokens: number
  activePowerUps: PowerUp[]
  currentEnvironment: Environment
}
```

**Responsibilities**:
- Main game loop (60 FPS target)
- State transitions between gameplay, Genie interface, and menus
- Input handling and event management
- Performance monitoring and optimization

### 2. Authentication Layer

**Purpose**: Handle Orange ID integration with debug bypass

**Key Classes**:
```javascript
class AuthManager {
  constructor(config)
  async authenticate()
  async logout()
  getUser()
  isAuthenticated()
  enableDebugMode()
}
```

**Integration Points**:
- Orange ID widget initialization
- Token validation and refresh
- Debug bypass for local development
- User profile data retrieval

### 3. Player and Ship System

**Purpose**: Player ship control, health, and power-ups

**Key Classes**:
```javascript
class PlayerShip {
  position: Vector2
  health: number
  lives: number
  powerUps: PowerUp[]
  weaponSystem: WeaponSystem
  
  update(deltaTime, input)
  takeDamage(amount)
  applyPowerUp(powerUp)
}

class WeaponSystem {
  fireRate: number
  projectileType: string
  spreadPattern: boolean
  
  fire()
  update(deltaTime)
}
```

### 4. Enemy System

**Purpose**: Enemy spawning, behavior, and environmental interactions

**Key Classes**:
```javascript
class EnemyManager {
  activeEnemies: Enemy[]
  spawnPatterns: SpawnPattern[]
  environmentModifiers: EnvironmentModifier[]
  
  spawnWave(waveConfig)
  updateEnemies(deltaTime)
  applyEnvironmentalEffects(environment)
}

class Enemy {
  type: EnemyType
  position: Vector2
  health: number
  behavior: BehaviorPattern
  environmentalEffectiveness: number
  
  update(deltaTime, environment)
  takeDamage(amount)
  calculateEnvironmentalModifier(environment)
}
```

**Enemy Types**:
- Water: Effective in aquatic environments, weak in lava/desert
- Fire: Strong in volcanic areas, weak in water/ice
- Air: Excellent in open space, impaired in dense forests
- Earth: Powerful in rocky terrain, slow in water
- Crystal: Energy-based, thrives in crystal caverns
- Shadow: Adaptable but has specific weaknesses per environment

### 5. Reality Warp System

**Purpose**: Core mechanic for battlefield transformation

**Key Classes**:
```javascript
class RealityWarpManager {
  currentEnvironment: Environment
  warpHistory: WarpEvent[]
  
  async initiatePlayerWarp(userInput, tokenCost)
  async initiateBossWarp(bossType)
  async generateEnvironment(prompt)
  applyEnvironmentalEffects(environment)
}

class Environment {
  type: EnvironmentType
  visualAssets: ImageAssets
  gameplayModifiers: GameplayModifiers
  enemyCompatibility: EnemyCompatibilityMap
  
  applyToGameState(gameState)
}
```

**Warp Process Flow**:
1. User input or AI boss trigger
2. LLM analysis for gameplay parameters
3. Fal.ai image generation
4. Environment object creation
5. Gameplay modifier application
6. Visual transition effect

### 6. Genie Interface System

**Purpose**: Between-level interface for purchases and warps

**Key Classes**:
```javascript
class GenieInterface {
  availablePowerUps: PowerUp[]
  availableWarps: WarpOption[]
  playerTokens: number
  
  show()
  hide()
  handlePowerUpPurchase(powerUp)
  handleWarpPurchase(warpOption)
  updateTokenDisplay()
}

class PowerUp {
  type: PowerUpType
  cost: number
  duration: number
  effect: PowerUpEffect
  
  apply(playerShip)
  remove(playerShip)
}
```

**Power-Up Types**:
- Extra Wingman: Additional ship providing covering fire
- Extra Life: Increases player life count by 1
- Spread Pattern Ammo: Wider projectile spread for better coverage

### 7. Token Economy Manager

**Purpose**: WISH token tracking, transactions, and raffle system

**Key Classes**:
```javascript
class TokenEconomyManager {
  playerBalance: number
  transactionHistory: Transaction[]
  raffleManager: RaffleManager
  
  awardTokens(amount, reason)
  spendTokens(amount, reason)
  calculateLevelReward(completionTime, performance)
  syncWithOrangeSDK()
}

class RaffleManager {
  currentPeriod: RafflePeriod
  participants: RaffleParticipant[]
  prizePool: number
  
  addParticipant(playerId, score)
  calculateWinners()
  distributePrizes()
}
```

**Token Flow**:
- **Income**: Level completion rewards (inverse to completion time)
- **Expenses**: Reality warps, power-ups
- **Raffle Pool**: 50% of net token profits from all players

### 8. AI Integration Layer

**Purpose**: Interface with Fal.ai and LLM services

**Key Classes**:
```javascript
class AIIntegrationManager {
  falaiClient: FalaiClient
  llmClient: LLMClient
  
  async generateBattlefield(prompt)
  async analyzeUserInput(input)
  handleAPIErrors(error)
}

class EnvironmentAnalyzer {
  async analyzePrompt(userInput)
  generateGameplayJSON(analysis)
  mapEnemyCompatibility(environmentType)
}
```

**LLM Analysis Output Format**:
```json
{
  "environmentType": "volcanic",
  "enemyCompatibility": {
    "fire": 1.5,
    "water": 0.3,
    "earth": 1.2,
    "air": 0.8,
    "crystal": 1.0,
    "shadow": 0.9
  },
  "gameplayModifiers": {
    "gravity": 0.8,
    "obstaclesDensity": 0.7,
    "spawnRate": 1.2,
    "visualEffects": ["lava_particles", "heat_distortion"]
  }
}
```

### 9. Orange SDK Integration

**Purpose**: Progress persistence and tournament platform compatibility

**Key Classes**:
```javascript
class OrangeSDKManager {
  gameData: GameData
  
  async loadPlayerData()
  async savePlayerData(data)
  async reportScore(score)
  handleGameEvents()
}
```

**Data Structure**:
```json
{
  "playerLevel": 15,
  "wishTokens": 1250,
  "completedLevels": [1, 2, 3, ...],
  "bestScores": {"level1": 15420, "level2": 18950},
  "powerUpsUnlocked": ["wingman", "extraLife", "spreadAmmo"],
  "totalPlayTime": 3600,
  "raffleParticipations": 5
}
```

## Data Models

### Core Game Data

```javascript
// Player progression and state
interface PlayerData {
  id: string
  currentLevel: number
  wishTokens: number
  lives: number
  health: number
  activePowerUps: PowerUp[]
  statistics: PlayerStatistics
}

// Level configuration and state
interface LevelData {
  id: number
  difficulty: number
  enemyWaves: WaveConfiguration[]
  environment: Environment
  bossType?: BossType
  completionReward: number
}

// Environment and warp data
interface Environment {
  type: EnvironmentType
  imageUrl: string
  gameplayModifiers: GameplayModifiers
  enemyEffectiveness: EnemyEffectivenessMap
  visualEffects: VisualEffect[]
}

// Token economy tracking
interface TokenTransaction {
  id: string
  playerId: string
  amount: number
  type: 'earned' | 'spent'
  reason: string
  timestamp: Date
}
```

## Error Handling

### API Integration Errors

**Fal.ai Service Failures**:
- Fallback to pre-generated environment assets
- Retry mechanism with exponential backoff
- User notification with option to continue with default environment

**LLM Analysis Failures**:
- Default environment parameters based on user input keywords
- Cached analysis results for common inputs
- Graceful degradation to standard enemy spawning

**Orange SDK Errors**:
- Local storage backup for critical game data
- Retry queue for failed save operations
- Offline mode with sync on reconnection

### Game State Errors

**Memory Management**:
- Asset cleanup between levels
- Enemy pool recycling
- Texture memory monitoring

**Performance Issues**:
- Dynamic quality scaling based on frame rate
- Particle system optimization
- Background asset loading

## Testing Strategy

### Unit Testing

**Core Systems**:
- Token economy calculations
- Enemy behavior and environmental effects
- Power-up application and removal
- Level progression logic

**Test Framework**: Jest with custom game engine mocks

### Integration Testing

**API Integration**:
- Fal.ai image generation with mock responses
- LLM analysis with predefined inputs/outputs
- Orange SDK save/load operations
- Orange ID authentication flow

### End-to-End Testing

**Game Flow Testing**:
- Complete level progression
- Token earning and spending cycles
- Reality warp functionality
- Raffle system operation

**Performance Testing**:
- Frame rate consistency across different devices
- Memory usage during extended play sessions
- Asset loading optimization

### User Acceptance Testing

**Gameplay Balance**:
- Token economy balance validation
- Difficulty curve assessment
- Power-up effectiveness evaluation
- Environmental strategy validation

## Security Considerations

### Token Economy Security

- Server-side validation of all token transactions
- Anti-cheat measures for score reporting
- Rate limiting on API calls
- Secure storage of sensitive game data

### Authentication Security

- Orange ID token validation
- Secure session management
- Debug mode restrictions for production
- User data privacy compliance

## Performance Optimization

### Rendering Optimization

- Canvas layer separation for static/dynamic content
- Sprite batching for similar objects
- Viewport culling for off-screen entities
- Texture atlas usage for reduced draw calls

### Memory Management

- Object pooling for frequently created/destroyed objects
- Asset preloading and lazy loading strategies
- Garbage collection optimization
- Memory leak prevention in game loops

### Network Optimization

- Asset caching strategies
- Compressed image formats
- API request batching
- Offline capability for core gameplay