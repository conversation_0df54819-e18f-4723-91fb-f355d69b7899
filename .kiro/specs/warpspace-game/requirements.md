# Requirements Document

## Introduction

WarpSpace is a vertical scrolling shooter game where players pilot a ship through procedurally generated dimensional rifts. The core innovation is a reality-warping mechanic powered by a deflationary token economy (WISH tokens). Players encounter a Genie between levels who facilitates reality warps that completely transform battlefields - from asteroid fields to crystal mazes, space to underwater caverns, or nebulae to volcanic landscapes. The game integrates with Orange ID for authentication, Orange SDK for progress tracking, and Fal.ai for dynamic battlefield generation.

## Requirements

### Requirement 1

**User Story:** As a player, I want to pilot a ship through vertical scrolling levels with enemy combat, so that I can progress through the game and earn WISH tokens.

#### Acceptance Criteria

1. WHEN a level starts THEN the system SHALL display a vertically scrolling battlefield with the player's ship at the bottom
2. WHEN the player uses controls THEN the ship SHALL move in response to input (left, right, up, down within screen bounds)
3. WHEN the player presses the fire button THEN the ship SHALL shoot projectiles upward
4. WHEN enemy projectiles or enemies contact the player's ship THEN the system SHALL reduce player health or lives
5. WHEN the player completes a level THEN the system SHALL award WISH tokens based on completion time and performance
6. WHEN the player's ship is destroyed and has no remaining lives THEN the system SHALL end the game session

### Requirement 2

**User Story:** As a player, I want to encounter a Genie between levels who facilitates reality warps, so that I can spend WISH tokens to transform the battlefield to my advantage.

#### Acceptance Criteria

1. WHEN a level is completed THEN the system SHALL display the Genie interface before the next level
2. WHEN the Genie appears THEN the system SHALL present options to spend WISH tokens on reality warps
3. WHEN the player selects a reality warp option THEN the system SHALL deduct the appropriate WISH tokens from their balance
4. WHEN a reality warp is purchased THEN the system SHALL generate a new battlefield environment using Fal.ai
5. WHEN the player has insufficient WISH tokens THEN the system SHALL disable unavailable warp options
6. IF the player chooses not to warp reality THEN the system SHALL proceed with the default space environment

### Requirement 3

**User Story:** As a player, I want to purchase power-ups between levels using WISH tokens, so that I can improve my chances of success in upcoming levels.

#### Acceptance Criteria

1. WHEN the Genie interface appears THEN the system SHALL display available power-ups (extra wingman, extra life, spread pattern ammo)
2. WHEN the player selects a power-up THEN the system SHALL deduct the appropriate WISH tokens and apply the enhancement
3. WHEN the player attempts to purchase a power-up they already have THEN the system SHALL prevent the duplicate purchase
4. WHEN the player has insufficient tokens for a power-up THEN the system SHALL disable that option
5. WHEN a power-up is active THEN the system SHALL display visual indicators of the enhancement

### Requirement 4

**User Story:** As a player, I want AI bosses to automatically warp reality against me, so that I face dynamic and challenging gameplay scenarios.

#### Acceptance Criteria

1. WHEN an AI boss level begins THEN the system SHALL automatically trigger a reality warp without player input
2. WHEN an AI boss warp occurs THEN the system SHALL generate terrain that spawns appropriate enemy types for that environment
3. WHEN the battlefield is warped to underwater THEN the system SHALL spawn water-type enemies
4. WHEN the battlefield is warped to volcanic THEN the system SHALL spawn fire-type enemies
5. WHEN the battlefield is warped to crystal caverns THEN the system SHALL spawn energy-based enemies

### Requirement 5

**User Story:** As a player, I want different enemy types to have advantages and disadvantages in different environments, so that reality warping creates meaningful strategic decisions.

#### Acceptance Criteria

1. WHEN water enemies are in lava environments THEN the system SHALL apply disadvantage modifiers to their behavior
2. WHEN ice enemies are in desert environments THEN the system SHALL reduce their effectiveness
3. WHEN flying enemies are in dense forest environments THEN the system SHALL impair their movement patterns
4. WHEN enemies are in their preferred environment THEN the system SHALL enhance their capabilities
5. WHEN environmental effects are active THEN the system SHALL display visual feedback indicating enemy status changes

### Requirement 6

**User Story:** As a player, I want to authenticate using Orange ID with a debug bypass option, so that I can access the game while supporting development and testing workflows.

#### Acceptance Criteria

1. WHEN the game starts THEN the system SHALL present Orange ID login interface
2. WHEN the player successfully authenticates with Orange ID THEN the system SHALL grant access to the game
3. WHEN a debug button is present THEN the system SHALL allow bypassing authentication for local development
4. WHEN authentication fails THEN the system SHALL display appropriate error messages
5. IF the player is not authenticated and not in debug mode THEN the system SHALL prevent game access

### Requirement 7

**User Story:** As a player, I want my progress and level completions saved via Orange SDK, so that I can continue my game across sessions and track my achievements.

#### Acceptance Criteria

1. WHEN a level is completed THEN the system SHALL save completion data via Orange SDK
2. WHEN the player's token balance changes THEN the system SHALL update the saved balance
3. WHEN the player starts a new session THEN the system SHALL load previous progress from Orange SDK
4. WHEN save operations fail THEN the system SHALL display error messages and attempt retry
5. WHEN the player achieves milestones THEN the system SHALL record achievements via Orange SDK

### Requirement 8

**User Story:** As a player, I want to participate in daily/weekly raffles based on my performance, so that I have additional motivation to play competitively and spend tokens strategically.

#### Acceptance Criteria

1. WHEN the raffle period ends THEN the system SHALL calculate top performers based on completion scores
2. WHEN raffle winners are selected THEN the system SHALL distribute Gold (250 WISH), Silver (150 WISH), and Bronze (100 WISH) jackpots
3. WHEN the raffle pool is calculated THEN the system SHALL use 50% of net token profits from all players
4. WHEN a player qualifies for the raffle THEN the system SHALL notify them of their eligibility
5. WHEN raffle results are determined THEN the system SHALL update winner token balances and send notifications

### Requirement 9

**User Story:** As a player, I want battlefield generation to be based on my input or AI boss prompts using Fal.ai, so that each reality warp creates unique and visually compelling environments.

#### Acceptance Criteria

1. WHEN a player initiates a reality warp THEN the system SHALL send their input to Fal.ai for battlefield generation
2. WHEN an AI boss triggers a warp THEN the system SHALL use predefined prompts for that boss type
3. WHEN Fal.ai returns generated content THEN the system SHALL integrate the new battlefield visuals
4. WHEN battlefield generation fails THEN the system SHALL fallback to default environments
5. WHEN a new battlefield is generated THEN the system SHALL apply corresponding JSON attributes for gameplay mechanics

### Requirement 10

**User Story:** As a developer, I want an LLM to analyze user input and generate JSON with environmental effects, so that reality warps have consistent gameplay impact beyond visual changes.

#### Acceptance Criteria

1. WHEN user input is received for reality warping THEN the system SHALL send it to the LLM for analysis
2. WHEN the LLM processes the input THEN the system SHALL receive JSON with enemy compatibility scores
3. WHEN JSON is generated THEN the system SHALL include terrain advantages, environmental effects, gravity settings, and obstacle density
4. WHEN the JSON is applied THEN the system SHALL modify enemy spawn patterns according to the specifications
5. WHEN LLM analysis fails THEN the system SHALL use default environmental parameters