import { PowerUpFactory } from '../systems/PowerUp.js';

/**
 * GenieInterface - Modal interface for purchasing power-ups and reality warps between levels
 * Provides a mystical Genie-themed UI for player purchases using WISH tokens
 */
export class GenieInterface {
    constructor(tokenManager, gameEngine) {
        this.tokenManager = tokenManager;
        this.gameEngine = gameEngine;
        
        // UI state
        this.isVisible = false;
        this.isInitialized = false;
        this.container = null;
        
        // Available power-ups
        this.availablePowerUps = PowerUpFactory.createAllPowerUps();
        this.activePowerUps = new Map(); // Track active power-ups by type
        
        // Callbacks
        this.onPowerUpPurchased = null;
        this.onWarpPurchased = null;
        this.onClose = null;
        
        // Animation state
        this.animationFrame = null;
        this.glowAnimation = 0;
        
        console.log('GenieInterface created');
    }
    
    /**
     * Initialize the Genie interface
     */
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // Create container element
            this.container = document.createElement('div');
            this.container.id = 'genie-interface';
            this.container.className = 'genie-interface hidden';
            
            // Add to document body
            document.body.appendChild(this.container);
            
            // Set up event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('GenieInterface initialized successfully');
            
        } catch (error) {
            console.error('GenieInterface initialization error:', error);
            throw error;
        }
    }
    
    /**
     * Show the Genie interface
     */
    show() {
        if (!this.isInitialized) {
            console.error('GenieInterface not initialized');
            return;
        }
        
        this.isVisible = true;
        this.updatePowerUpAvailability();
        this.render();
        this.container.classList.remove('hidden');
        this.container.classList.add('visible');
        
        // Start glow animation
        this.startGlowAnimation();
        
        console.log('GenieInterface shown');
    }
    
    /**
     * Hide the Genie interface
     */
    hide() {
        if (!this.isVisible) return;
        
        this.isVisible = false;
        this.container.classList.remove('visible');
        this.container.classList.add('hidden');
        
        // Stop glow animation
        this.stopGlowAnimation();
        
        console.log('GenieInterface hidden');
    }
    
    /**
     * Update power-up availability based on current game state
     */
    updatePowerUpAvailability() {
        const playerTokens = this.tokenManager.getBalance();
        const playerShip = this.gameEngine.playerShip;
        
        this.availablePowerUps.forEach(powerUp => {
            const availability = powerUp.canPurchase(playerShip, playerTokens);
            powerUp.availability = availability;
        });
    }
    
    /**
     * Render the Genie interface
     */
    render() {
        if (!this.container) return;
        
        const playerTokens = this.tokenManager.getBalance();
        
        this.container.innerHTML = `
            <div class="genie-modal">
                <div class="genie-backdrop" id="genie-backdrop"></div>
                <div class="genie-content">
                    <div class="genie-header">
                        <div class="genie-character">
                            <div class="genie-lamp">🪔</div>
                            <div class="genie-smoke"></div>
                        </div>
                        <h1 class="genie-title">The Mystical Genie</h1>
                        <p class="genie-subtitle">Your wishes are my command, traveler...</p>
                        <div class="token-display">
                            <span class="token-icon">✨</span>
                            <span class="token-amount">${playerTokens}</span>
                            <span class="token-label">WISH Tokens</span>
                        </div>
                    </div>
                    
                    <div class="genie-body">
                        <div class="power-ups-section">
                            <h2 class="section-title">Power-Up Enchantments</h2>
                            <div class="power-ups-grid">
                                ${this.renderPowerUps()}
                            </div>
                        </div>
                        
                        <div class="reality-warp-section">
                            <h2 class="section-title">Reality Warp Magic</h2>
                            <div class="warp-options">
                                <div class="warp-card coming-soon">
                                    <div class="warp-icon">🌌</div>
                                    <h3>Reality Warp</h3>
                                    <p>Transform the battlefield to your advantage</p>
                                    <div class="warp-cost">Coming Soon</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="genie-footer">
                        <button id="genie-close-btn" class="genie-button secondary">
                            Continue Journey
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Set up button event listeners
        this.setupButtonEventListeners();
    }
    
    /**
     * Render power-up cards
     */
    renderPowerUps() {
        return this.availablePowerUps.map(powerUp => {
            const availability = powerUp.availability || { canPurchase: false, reason: 'unknown' };
            const isActive = this.activePowerUps.has(powerUp.type);
            const canPurchase = availability.canPurchase && !isActive;
            
            let statusClass = '';
            let statusText = '';
            let buttonText = `Purchase (${powerUp.cost} ✨)`;
            
            if (isActive) {
                statusClass = 'active';
                statusText = 'Active';
                buttonText = 'Already Active';
            } else if (!availability.canPurchase) {
                statusClass = 'unavailable';
                if (availability.reason === 'insufficient_tokens') {
                    statusText = 'Insufficient Tokens';
                    buttonText = `Need ${powerUp.cost - this.tokenManager.getBalance()} more ✨`;
                } else {
                    statusText = 'Unavailable';
                    buttonText = 'Cannot Purchase';
                }
            }
            
            return `
                <div class="power-up-card ${statusClass}" data-power-up="${powerUp.type}">
                    <div class="power-up-icon">${powerUp.icon}</div>
                    <h3 class="power-up-name">${this.formatPowerUpName(powerUp.type)}</h3>
                    <p class="power-up-description">${powerUp.description}</p>
                    <div class="power-up-details">
                        <div class="power-up-cost">
                            <span class="cost-amount">${powerUp.cost}</span>
                            <span class="cost-icon">✨</span>
                        </div>
                        <div class="power-up-duration">
                            ${powerUp.duration ? `${Math.ceil(powerUp.duration / 1000)}s` : 'Permanent'}
                        </div>
                    </div>
                    <div class="power-up-status">${statusText}</div>
                    <button class="power-up-button ${canPurchase ? 'primary' : 'disabled'}" 
                            data-power-up="${powerUp.type}"
                            ${!canPurchase ? 'disabled' : ''}>
                        ${buttonText}
                    </button>
                </div>
            `;
        }).join('');
    }
    
    /**
     * Format power-up type name for display
     */
    formatPowerUpName(type) {
        switch (type) {
            case 'EXTRA_LIFE':
                return 'Extra Life';
            case 'SPREAD_AMMO':
                return 'Spread Ammo';
            case 'EXTRA_WINGMAN':
                return 'Extra Wingman';
            default:
                return type.replace(/_/g, ' ').toLowerCase()
                    .replace(/\b\w/g, l => l.toUpperCase());
        }
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Handle escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.close();
            }
        });
    }
    
    /**
     * Set up button event listeners after rendering
     */
    setupButtonEventListeners() {
        // Close button
        const closeBtn = this.container.querySelector('#genie-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close());
        }
        
        // Backdrop click to close
        const backdrop = this.container.querySelector('#genie-backdrop');
        if (backdrop) {
            backdrop.addEventListener('click', () => this.close());
        }
        
        // Power-up purchase buttons
        const powerUpButtons = this.container.querySelectorAll('.power-up-button:not(.disabled)');
        powerUpButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const powerUpType = e.target.dataset.powerUp;
                this.handlePowerUpPurchase(powerUpType);
            });
        });
    }
    
    /**
     * Handle power-up purchase
     */
    handlePowerUpPurchase(powerUpType) {
        const powerUp = this.availablePowerUps.find(p => p.type === powerUpType);
        if (!powerUp) {
            console.error('Power-up not found:', powerUpType);
            return;
        }
        
        // Check if purchase is valid
        const playerTokens = this.tokenManager.getBalance();
        const playerShip = this.gameEngine.playerShip;
        const availability = powerUp.canPurchase(playerShip, playerTokens);
        
        if (!availability.canPurchase) {
            console.warn('Cannot purchase power-up:', availability.reason);
            return;
        }
        
        // Spend tokens
        const spendResult = this.tokenManager.spendTokens(powerUp.cost, `power_up_${powerUpType.toLowerCase()}`);
        if (!spendResult.success) {
            console.error('Failed to spend tokens:', spendResult.reason);
            return;
        }
        
        // Apply power-up
        const applyResult = powerUp.apply(playerShip);
        if (!applyResult) {
            console.error('Failed to apply power-up');
            // Refund tokens
            this.tokenManager.awardTokens(powerUp.cost, 'power_up_refund');
            return;
        }
        
        // Track active power-up
        this.activePowerUps.set(powerUpType, powerUp);
        
        // Update UI
        this.updatePowerUpAvailability();
        this.render();
        
        // Trigger callback
        if (this.onPowerUpPurchased) {
            this.onPowerUpPurchased(powerUp, spendResult);
        }
        
        console.log(`Power-up purchased: ${powerUpType} for ${powerUp.cost} tokens`);
    }
    
    /**
     * Close the interface
     */
    close() {
        this.hide();
        
        if (this.onClose) {
            this.onClose();
        }
    }
    
    /**
     * Start glow animation
     */
    startGlowAnimation() {
        if (this.animationFrame) return;
        
        const animate = () => {
            this.glowAnimation += 0.05;
            
            // Apply glow effect to lamp and tokens
            const lamp = this.container.querySelector('.genie-lamp');
            const tokenIcon = this.container.querySelector('.token-icon');
            
            if (lamp) {
                const glow = Math.sin(this.glowAnimation) * 0.5 + 0.5;
                lamp.style.filter = `drop-shadow(0 0 ${10 + glow * 10}px #ffd700)`;
            }
            
            if (tokenIcon) {
                const glow = Math.sin(this.glowAnimation + 1) * 0.5 + 0.5;
                tokenIcon.style.filter = `drop-shadow(0 0 ${5 + glow * 5}px #00ffff)`;
            }
            
            if (this.isVisible) {
                this.animationFrame = requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    /**
     * Stop glow animation
     */
    stopGlowAnimation() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }
    }
    
    /**
     * Update active power-ups (call from game loop)
     */
    updateActivePowerUps(deltaTime) {
        const playerShip = this.gameEngine.playerShip;
        
        for (const [type, powerUp] of this.activePowerUps) {
            const stillActive = powerUp.update(deltaTime, playerShip);
            
            if (!stillActive) {
                this.activePowerUps.delete(type);
                console.log(`Power-up expired: ${type}`);
            }
        }
    }
    
    /**
     * Set callbacks
     */
    setOnPowerUpPurchased(callback) {
        this.onPowerUpPurchased = callback;
    }
    
    setOnWarpPurchased(callback) {
        this.onWarpPurchased = callback;
    }
    
    setOnClose(callback) {
        this.onClose = callback;
    }
    
    /**
     * Cleanup resources
     */
    destroy() {
        this.stopGlowAnimation();
        
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        this.container = null;
        this.isInitialized = false;
        
        console.log('GenieInterface destroyed');
    }
}
