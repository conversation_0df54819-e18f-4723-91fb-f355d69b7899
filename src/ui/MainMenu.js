import { AuthManager } from '../auth/AuthManager.js';

export class MainMenu {
  constructor(gameEngine) {
    this.gameEngine = gameEngine;
    this.authManager = new AuthManager({
      tenantId: 'orange-abc123', // Replace with actual tenant ID
      subscriptionKey: 'your_API_Key' // Replace with actual API key
    });
    
    this.container = null;
    this.isVisible = false;
    this.setupEventListeners();
  }

  async initialize() {
    this.createMenuHTML();

    console.log('MainMenu initializing, debug mode:', this.authManager.config.debugMode);

    // Initialize Orange ID if not in debug mode
    if (!this.authManager.config.debugMode) {
      try {
        console.log('Initializing Orange ID...');
        await this.authManager.initializeOrangeID();
        console.log('Orange ID initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Orange ID:', error);
        this.showError(`Authentication system failed to load: ${error.message}. You can use debug login instead.`);
      }
    } else {
      console.log('Debug mode enabled - Orange ID initialization skipped');
    }

    this.authManager.onAuthStateChange((isAuthenticated, data) => {
      if (isAuthenticated) {
        this.onAuthenticationSuccess(data);
      } else {
        this.onAuthenticationFailure(data);
      }
    });
  }

  createMenuHTML() {
    this.container = document.createElement('div');
    this.container.id = 'main-menu';
    this.container.className = 'main-menu';
    
    this.container.innerHTML = `
      <div class="menu-background">
        <div class="stars"></div>
        <div class="menu-content">
          <h1 class="game-title">WARPSPACE</h1>
          <p class="game-subtitle">Reality Warping Shooter</p>
          
          <div class="auth-section" id="auth-section">
            <div class="orange-id-container">
              <div id="bedrock-login-widget"></div>
            </div>
            
            <div class="debug-section" id="debug-section">
              <button id="debug-login-btn" class="debug-button">
                🔧 Debug Login (Skip Auth)
              </button>
              <p style="color: #888; font-size: 12px; margin-top: 10px;">
                Debug mode: ${this.authManager.config.debugMode ? 'ON' : 'OFF'}
              </p>
            </div>
          </div>
          
          <div class="game-controls" id="game-controls" style="display: none;">
            <button id="start-game-btn" class="menu-button primary">
              Start Game
            </button>
            <button id="genie-shop-btn" class="menu-button">
              🪔 Genie Shop
            </button>
            <button id="instructions-btn" class="menu-button">
              How to Play
            </button>
            <button id="logout-btn" class="menu-button secondary">
              Logout
            </button>
          </div>
          
          <div class="user-info" id="user-info" style="display: none;">
            <p>Welcome, <span id="user-name"></span>!</p>
            <p>WISH Tokens: <span id="token-balance">0</span></p>
          </div>
          
          <div class="error-message" id="error-message" style="display: none;"></div>
        </div>
      </div>
    `;
    
    document.body.appendChild(this.container);
  }

  setupEventListeners() {
    document.addEventListener('click', (event) => {
      if (event.target.id === 'debug-login-btn') {
        this.authManager.enableDebugMode();
      } else if (event.target.id === 'start-game-btn') {
        this.startGame();
      } else if (event.target.id === 'genie-shop-btn') {
        this.showGenieShop();
      } else if (event.target.id === 'instructions-btn') {
        this.showInstructions();
      } else if (event.target.id === 'logout-btn') {
        this.logout();
      }
    });
  }

  onAuthenticationSuccess(user) {
    console.log('Authentication successful:', user);
    
    // Hide auth section, show game controls
    document.getElementById('auth-section').style.display = 'none';
    document.getElementById('game-controls').style.display = 'block';
    document.getElementById('user-info').style.display = 'block';
    
    // Update user info
    document.getElementById('user-name').textContent = user.name || user.email || 'Player';
    
    // Load user's token balance (would come from Orange SDK later)
    this.loadUserTokenBalance();
    
    this.hideError();
  }

  onAuthenticationFailure(error) {
    console.error('Authentication failed:', error);
    
    // Show auth section, hide game controls
    document.getElementById('auth-section').style.display = 'block';
    document.getElementById('game-controls').style.display = 'none';
    document.getElementById('user-info').style.display = 'none';
    
    if (error && typeof error === 'object') {
      this.showError('Authentication failed. Please try again.');
    }
  }

  async loadUserTokenBalance() {
    // Placeholder for Orange SDK integration
    // For now, show default balance
    document.getElementById('token-balance').textContent = '100';
  }

  startGame() {
    if (!this.authManager.isAuthenticated) {
      this.showError('Please authentication first');
      return;
    }

    this.hide();
    this.gameEngine.startGame(this.authManager.getUser());
  }

  showGenieShop() {
    if (!this.authManager.isAuthenticated) {
      this.showError('Please authenticate first');
      return;
    }

    console.log('Opening Genie Shop from main menu...');

    // Get the game engine's genie interface
    if (this.gameEngine && this.gameEngine.genieInterface) {
      // Give some debug tokens if the player has none
      if (this.gameEngine.tokenManager && this.gameEngine.tokenManager.getBalance() < 100) {
        this.gameEngine.tokenManager.awardTokens(500, 'debug_menu_tokens');
        console.log('Awarded debug tokens for testing');
      }

      this.gameEngine.genieInterface.show();
    } else {
      this.showError('Genie Shop not available. Please start the game first.');
    }
  }

  showInstructions() {
    const instructions = `
      WARPSPACE - How to Play
      
      🚀 CONTROLS:
      • Arrow Keys / WASD: Move ship
      • Spacebar: Fire weapons
      • P: Pause game
      
      💎 WISH TOKENS:
      • Earn tokens by completing levels quickly
      • Spend tokens on reality warps and power-ups
      • Visit the Genie between levels to make purchases
      
      🌟 REALITY WARPS:
      • Transform battlefields with AI-generated environments
      • Different environments affect enemy effectiveness
      • Strategic warping can give you tactical advantages
      
      🎯 POWER-UPS:
      • Extra Wingman: Additional firepower
      • Extra Life: Survive longer
      • Spread Ammo: Wider shot pattern
    `;
    
    alert(instructions); // Replace with proper modal later
  }

  async logout() {
    await this.authManager.logout();
    this.onAuthenticationFailure(null);
  }

  show() {
    if (this.container) {
      this.container.style.display = 'block';
      this.isVisible = true;
    }
  }

  hide() {
    if (this.container) {
      this.container.style.display = 'none';
      this.isVisible = false;
    }
  }

  showError(message) {
    const errorElement = document.getElementById('error-message');
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.style.display = 'block';
    }
  }

  hideError() {
    const errorElement = document.getElementById('error-message');
    if (errorElement) {
      errorElement.style.display = 'none';
    }
  }

  destroy() {
    if (this.container) {
      document.body.removeChild(this.container);
      this.container = null;
    }
  }
}
