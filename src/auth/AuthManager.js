/**
 * AuthManager - Orange ID authentication wrapper
 * Handles Orange ID integration with authentication state management
 */
export class AuthManager {
    constructor(config = {}) {
        this.config = {
            baseUrl: 'https://api.bedrockpassport.com',
            authCallbackUrl: window.location.origin,
            tenantId: config.tenantId || 'orange-abc123',
            subscriptionKey: config.subscriptionKey || 'your_API_Key',
            debugMode: config.debugMode || this.isDebugEnvironment()
        };
        
        this.user = null;
        this.token = null;
        this.refreshToken = null;
        this.isAuthenticated = false;
        this.authCallbacks = [];
    }

    isDebugEnvironment() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.search.includes('debug=true');
    }

    async initializeOrangeID() {
        if (this.config.debugMode) {
            console.log('Debug mode enabled - Orange ID initialization skipped');
            return true;
        }

        return new Promise((resolve, reject) => {
            // Add a small delay to ensure scripts are fully loaded
            setTimeout(() => {
                // Check if all required libraries are loaded
                console.log('Checking required libraries:', {
                    React: !!window.React,
                    ReactDOM: !!window.ReactDOM,
                    Bedrock: !!window.Bedrock
                });

                if (!window.React || !window.ReactDOM || !window.Bedrock) {
                    console.error('Required libraries not loaded:', {
                        React: !!window.React,
                        ReactDOM: !!window.ReactDOM,
                        Bedrock: !!window.Bedrock
                    });
                    reject(new Error('Required libraries failed to load. Please check your internet connection.'));
                    return;
                }

                console.log('All libraries loaded, setting up Orange ID widget...');
                this.setupOrangeIDWidget(resolve, reject);
            }, 100);
        });
    }



    setupOrangeIDWidget(resolve, reject) {
        try {
            const container = document.getElementById('bedrock-login-widget');
            if (!container) {
                reject(new Error('Orange ID container not found'));
                return;
            }

            const root = ReactDOM.createRoot(container);

            // Check for existing auth tokens in URL
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            const refreshToken = urlParams.get('refreshToken');

            if (token && refreshToken) {
                // Handle callback processing
                root.render(
                    React.createElement(
                        window.Bedrock.BedrockPassportProvider,
                        this.config,
                        React.createElement(this.createCallbackProcessor(token, refreshToken))
                    )
                );
            } else {
                // Normal login flow
                root.render(
                    React.createElement(
                        window.Bedrock.BedrockPassportProvider,
                        this.config,
                        React.createElement(window.Bedrock.LoginPanel, {
                            title: "Sign in to",
                            logo: "https://irp.cdn-website.com/e81c109a/dms3rep/multi/orange-web3-logo-v2a-20241018.svg",
                            logoAlt: "Orange Web3",
                            walletButtonText: "Connect Wallet",
                            showConnectWallet: false,
                            separatorText: "OR",
                            features: {
                                enableWalletConnect: false,
                                enableAppleLogin: true,
                                enableGoogleLogin: true,
                                enableEmailLogin: false,
                            },
                            onLoginSuccess: (user, tokens) => {
                                console.log('Orange ID login success:', user);
                                this.handleAuthSuccess(tokens.accessToken, tokens.refreshToken);
                            },
                            onLoginError: (error) => {
                                console.error('Orange ID login error:', error);
                                this.handleAuthError(error);
                            }
                        })
                    )
                );
            }

            // Listen for authentication events
            this.setupAuthEventListeners();
            resolve(true);
        } catch (error) {
            console.error('Error setting up Orange ID widget:', error);
            reject(error);
        }
    }

    createCallbackProcessor(token, refreshToken) {
        const self = this;
        return function CallbackProcessor() {
            const { loginCallback } = window.Bedrock.useBedrockPassport();

            React.useEffect(() => {
                async function processCallback() {
                    try {
                        const success = await loginCallback(token, refreshToken);
                        if (success) {
                            console.log('Callback processing successful');
                            // The auth success will be handled by the onLoginSuccess callback
                        } else {
                            self.handleAuthError(new Error('Login callback failed'));
                        }
                    } catch (error) {
                        console.error('Callback processing error:', error);
                        self.handleAuthError(error);
                    }
                }
                processCallback();
            }, [loginCallback]);

            return React.createElement('div', {
                style: { textAlign: 'center', padding: '20px', color: '#00ffff' }
            }, 'Processing authentication...');
        };
    }

    setupAuthEventListeners() {
        // Listen for custom auth events (for additional integration points)
        window.addEventListener('orangeAuthSuccess', (event) => {
            this.handleAuthSuccess(event.detail.token, event.detail.refreshToken);
        });

        window.addEventListener('orangeAuthError', (event) => {
            this.handleAuthError(event.detail.error);
        });
    }

    async handleAuthSuccess(token, refreshToken) {
        this.token = token;
        this.refreshToken = refreshToken;
        
        try {
            const userProfile = await this.validateToken(token);
            this.user = userProfile;
            this.isAuthenticated = true;
            
            // Clear URL parameters
            const url = new URL(window.location);
            url.searchParams.delete('token');
            url.searchParams.delete('refreshToken');
            window.history.replaceState({}, document.title, url);
            
            this.notifyAuthCallbacks(true, this.user);
        } catch (error) {
            this.handleAuthError(error);
        }
    }

    handleAuthError(error) {
        console.error('Authentication error:', error);
        this.isAuthenticated = false;
        this.user = null;
        this.token = null;
        this.refreshToken = null;
        this.notifyAuthCallbacks(false, error);
    }

    async validateToken(token) {
        const response = await fetch('https://api.bedrockpassport.com/api/v1/auth/user', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Token validation failed');
        }

        return await response.json();
    }

    enableDebugMode() {
        this.config.debugMode = true;
        this.user = {
            id: 'debug-user-123',
            email: '<EMAIL>',
            name: 'Debug User',
            provider: 'debug'
        };
        this.isAuthenticated = true;
        this.notifyAuthCallbacks(true, this.user);
    }

    async logout() {
        this.user = null;
        this.token = null;
        this.refreshToken = null;
        this.isAuthenticated = false;
        this.notifyAuthCallbacks(false, null);
    }

    getUser() {
        return this.user;
    }

    onAuthStateChange(callback) {
        this.authCallbacks.push(callback);
    }

    notifyAuthCallbacks(isAuthenticated, data) {
        this.authCallbacks.forEach(callback => {
            callback(isAuthenticated, data);
        });
    }
}
