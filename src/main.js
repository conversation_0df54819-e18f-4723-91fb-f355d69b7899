import { GameEngine } from './core/GameEngine.js';
import { MainMenu } from './ui/MainMenu.js';

class Game {
  constructor() {
    this.gameEngine = null;
    this.mainMenu = null;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Get canvas element
      const canvas = document.getElementById('gameCanvas');
      if (!canvas) {
        throw new Error('Game canvas not found');
      }

      // Initialize game engine
      this.gameEngine = new GameEngine(canvas);
      await this.gameEngine.init();

      // Initialize main menu
      this.mainMenu = new MainMenu(this.gameEngine);
      await this.mainMenu.initialize();

      // Show main menu
      this.mainMenu.show();

      this.isInitialized = true;
      console.log('Game initialized successfully');
    } catch (error) {
      console.error('Failed to initialize game:', error);
      this.showInitializationError(error);
    }
  }

  showInitializationError(error) {
    document.body.innerHTML = `
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: #0a0a2e;
        color: white;
        font-family: Arial, sans-serif;
        text-align: center;
      ">
        <div>
          <h1>Game Initialization Failed</h1>
          <p>Error: ${error.message}</p>
          <button onclick="location.reload()" style="
            background: #00ffff;
            color: #0a0a2e;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
          ">
            Retry
          </button>
        </div>
      </div>
    `;
  }
}

// Global game instance
let game = null;

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  game = new Game();
  await game.initialize();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
  if (game && game.gameEngine) {
    if (document.hidden) {
      game.gameEngine.pause();
    } else {
      game.gameEngine.resume();
    }
  }
});

// Handle page unload to save final data
window.addEventListener('beforeunload', async (event) => {
  if (game && game.gameEngine) {
    // Save final data before page unload
    await game.gameEngine.destroy();
  }
});

// Handle page unload for mobile devices
document.addEventListener('pagehide', async (event) => {
  if (game && game.gameEngine) {
    await game.gameEngine.destroy();
  }
});
