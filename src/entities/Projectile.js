import { GameObject } from '../utils/GameObject.js';
import { Vector2 } from '../utils/Vector2.js';

/**
 * Projectile class - Represents bullets/projectiles fired by ships
 * Handles movement, collision detection, and visual effects
 */
export class Projectile extends GameObject {
    constructor(x = 0, y = 0) {
        super(x, y);
        
        // Projectile properties
        this.speed = 600; // pixels per second
        this.damage = 1;
        this.lifetime = 3000; // milliseconds
        this.age = 0;
        
        // Visual properties
        this.width = 4;
        this.height = 12;
        this.collisionRadius = 3;
        this.color = '#FFD700'; // Gold color
        this.trailColor = '#FFA500'; // Orange trail
        
        // Trail effect properties
        this.trailPositions = [];
        this.maxTrailLength = 8;
        this.trailFadeRate = 0.8;
        
        // Projectile type and owner
        this.type = 'player'; // 'player' or 'enemy'
        this.owner = null;
        
        // Add projectile tag
        this.addTag('projectile');
        
        // Initialize as inactive
        this.active = false;
        this.visible = false;
    }
    
    /**
     * Initialize projectile with starting parameters
     * @param {Vector2} position - Starting position
     * @param {Vector2} direction - Direction vector (normalized)
     * @param {number} speed - Projectile speed
     * @param {string} type - Projectile type ('player' or 'enemy')
     * @param {GameObject} owner - Object that fired this projectile
     */
    initialize(position, direction, speed = 600, type = 'player', owner = null) {
        this.position.setFromVector(position);
        this.velocity = direction.normalize().multiply(speed);
        this.speed = speed;
        this.type = type;
        this.owner = owner;
        this.age = 0;
        
        // Clear trail
        this.trailPositions = [];
        
        // Set visual properties based on type
        this.setupVisualsByType();
        
        // Activate projectile
        this.active = true;
        this.visible = true;
        this.destroyed = false;
        
        // Add appropriate tags
        this.tags.clear();
        this.addTag('projectile');
        this.addTag(type + 'Projectile');
        
        return this;
    }
    
    /**
     * Set up visual properties based on projectile type
     */
    setupVisualsByType() {
        switch (this.type) {
            case 'player':
                this.color = '#FFD700'; // Gold
                this.trailColor = '#FFA500'; // Orange
                this.width = 4;
                this.height = 12;
                break;
            case 'enemy':
                this.color = '#FF4444'; // Red
                this.trailColor = '#FF8888'; // Light red
                this.width = 3;
                this.height = 8;
                break;
            default:
                this.color = '#FFFFFF'; // White
                this.trailColor = '#CCCCCC'; // Light gray
                break;
        }
    }
    
    /**
     * Update projectile movement and lifetime
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    update(deltaTime) {
        if (!this.active) return;
        
        // Update age
        this.age += deltaTime;
        
        // Check lifetime
        if (this.age >= this.lifetime) {
            this.destroy();
            return;
        }
        
        // Store current position for trail effect
        this.updateTrail();
        
        // Update position using parent class physics
        super.update(deltaTime);
        
        // Update collision bounds
        this.updateCollisionBounds();
    }
    
    /**
     * Update trail effect positions
     */
    updateTrail() {
        // Add current position to trail
        this.trailPositions.unshift(this.position.clone());
        
        // Limit trail length
        if (this.trailPositions.length > this.maxTrailLength) {
            this.trailPositions.pop();
        }
    }
    
    /**
     * Render projectile with trail effects
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} interpolation - Interpolation factor for smooth rendering
     */
    render(ctx, interpolation = 0) {
        if (!this.visible) return;
        
        // Calculate interpolated position
        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));
        
        ctx.save();
        
        // Draw trail effect
        this.renderTrail(ctx, interpolation);
        
        // Draw main projectile
        this.renderProjectile(ctx, renderPos);
        
        // Draw debug info if enabled
        if (window.DEBUG_MODE) {
            this.renderDebugInfo(ctx, renderPos);
        }
        
        ctx.restore();
    }
    
    /**
     * Render projectile trail effect
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} interpolation - Interpolation factor
     */
    renderTrail(ctx, interpolation) {
        if (this.trailPositions.length < 2) return;
        
        ctx.strokeStyle = this.trailColor;
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        
        // Draw trail segments with fading alpha
        for (let i = 0; i < this.trailPositions.length - 1; i++) {
            const alpha = Math.pow(this.trailFadeRate, i);
            const currentPos = this.trailPositions[i];
            const nextPos = this.trailPositions[i + 1];
            
            // Apply interpolation to the most recent trail segment
            let renderCurrentPos = currentPos;
            if (i === 0) {
                const interpolatedOffset = this.velocity.multiply(interpolation / 1000);
                renderCurrentPos = currentPos.add(interpolatedOffset);
            }
            
            ctx.globalAlpha = alpha;
            ctx.beginPath();
            ctx.moveTo(renderCurrentPos.x, renderCurrentPos.y);
            ctx.lineTo(nextPos.x, nextPos.y);
            ctx.stroke();
        }
        
        ctx.globalAlpha = 1.0;
    }
    
    /**
     * Render the main projectile body
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {Vector2} renderPos - Interpolated render position
     */
    renderProjectile(ctx, renderPos) {
        ctx.translate(renderPos.x, renderPos.y);
        
        // Main projectile body - simple rectangle for now
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
        
        // Add a bright center
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(-1, -this.height / 2, 2, this.height);
    }
    
    /**
     * Draw player projectile shape
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawPlayerProjectile(ctx) {
        // Elongated diamond shape
        ctx.beginPath();
        ctx.moveTo(0, -this.height / 2); // Top point
        ctx.lineTo(this.width / 2, 0); // Right point
        ctx.lineTo(0, this.height / 2); // Bottom point
        ctx.lineTo(-this.width / 2, 0); // Left point
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // Inner glow effect
        ctx.fillStyle = this.lightenColor(this.color, 0.3);
        ctx.beginPath();
        ctx.moveTo(0, -this.height / 4);
        ctx.lineTo(this.width / 4, 0);
        ctx.lineTo(0, this.height / 4);
        ctx.lineTo(-this.width / 4, 0);
        ctx.closePath();
        ctx.fill();
    }
    
    /**
     * Draw enemy projectile shape
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawEnemyProjectile(ctx) {
        // Simple oval shape
        ctx.beginPath();
        ctx.ellipse(0, 0, this.width / 2, this.height / 2, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        
        // Inner highlight
        ctx.fillStyle = this.lightenColor(this.color, 0.2);
        ctx.beginPath();
        ctx.ellipse(0, -this.height / 6, this.width / 4, this.height / 4, 0, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Render debug information
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {Vector2} renderPos - Render position
     */
    renderDebugInfo(ctx, renderPos) {
        ctx.resetTransform();
        
        // Collision circle
        ctx.strokeStyle = '#FF0000';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(renderPos.x, renderPos.y, this.collisionRadius, 0, Math.PI * 2);
        ctx.stroke();
        
        // Velocity vector
        ctx.strokeStyle = '#00FF00';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(renderPos.x, renderPos.y);
        const velocityEnd = renderPos.add(this.velocity.multiply(0.05)); // Scale for visibility
        ctx.lineTo(velocityEnd.x, velocityEnd.y);
        ctx.stroke();
        
        // Age indicator
        ctx.fillStyle = '#FFFF00';
        ctx.font = '10px Arial';
        ctx.fillText(`${Math.floor(this.age)}ms`, renderPos.x + 10, renderPos.y - 10);
    }
    
    /**
     * Check if projectile is out of bounds
     * @param {object} bounds - Boundary object with left, right, top, bottom properties
     * @returns {boolean} True if out of bounds
     */
    isOutOfBounds(bounds) {
        const margin = Math.max(this.width, this.height);
        return this.position.x < bounds.left - margin ||
               this.position.x > bounds.right + margin ||
               this.position.y < bounds.top - margin ||
               this.position.y > bounds.bottom + margin;
    }
    
    /**
     * Handle collision with another object
     * @param {GameObject} other - Object that was hit
     */
    onCollision(other) {
        // Override in subclasses for specific collision behavior
        this.destroy();
    }
    
    /**
     * Reset projectile to inactive state (for object pooling)
     */
    reset() {
        super.reset();
        this.age = 0;
        this.trailPositions = [];
        this.type = 'player';
        this.owner = null;
        this.speed = 600;
        this.damage = 1;
        this.lifetime = 3000;
    }
    
    /**
     * Utility function to darken a color
     * @param {string} color - Hex color string
     * @param {number} factor - Darkening factor (0-1)
     * @returns {string} Darkened color
     */
    darkenColor(color, factor) {
        // Simple darkening - in a real implementation you might want more sophisticated color manipulation
        const hex = color.replace('#', '');
        const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - factor));
        const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - factor));
        const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - factor));
        return `rgb(${r}, ${g}, ${b})`;
    }
    
    /**
     * Utility function to lighten a color
     * @param {string} color - Hex color string
     * @param {number} factor - Lightening factor (0-1)
     * @returns {string} Lightened color
     */
    lightenColor(color, factor) {
        const hex = color.replace('#', '');
        const r = Math.min(255, Math.floor(parseInt(hex.substr(0, 2), 16) * (1 + factor)));
        const g = Math.min(255, Math.floor(parseInt(hex.substr(2, 2), 16) * (1 + factor)));
        const b = Math.min(255, Math.floor(parseInt(hex.substr(4, 2), 16) * (1 + factor)));
        return `rgb(${r}, ${g}, ${b})`;
    }
}