import { GameObject } from '../utils/GameObject.js';
import { Vector2 } from '../utils/Vector2.js';
import { WeaponSystem } from '../systems/WeaponSystem.js';

/**
 * PlayerShip class - Represents the player's ship with movement, rendering, and collision
 * Implements smooth movement with boundary checking and basic sprite rendering
 */
export class PlayerShip extends GameObject {
    constructor(x, y, canvasWidth, canvasHeight, gameObjectManager = null) {
        super(x, y);
        
        // Canvas boundaries for movement constraints
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        
        // Ship properties
        this.maxSpeed = 300; // pixels per second
        this.acceleration = 800; // pixels per second squared
        this.friction = 0.85; // velocity damping factor
        
        // Health and lives system
        this.maxHealth = 100;
        this.health = this.maxHealth;
        this.maxLives = 3;
        this.lives = this.maxLives;
        this.isInvulnerable = false;
        this.invulnerabilityDuration = 2000; // 2 seconds in milliseconds
        this.invulnerabilityTimer = 0;
        this.isDestroyed = false;
        
        // Damage feedback
        this.damageFlashTimer = 0;
        this.damageFlashDuration = 200; // 200ms flash duration
        this.isFlashing = false;
        
        // Visual properties
        this.width = 32;
        this.height = 48;
        this.collisionRadius = 16;
        
        // Ship boundaries (keep ship fully on screen)
        this.boundaryPadding = Math.max(this.width, this.height) / 2;
        
        // Animation properties
        this.animationTime = 0;
        this.thrusterAnimationSpeed = 8; // cycles per second
        
        // Movement state
        this.isMoving = false;
        this.movementInput = new Vector2(0, 0);
        
        // Weapon system
        this.weaponSystem = null;
        if (gameObjectManager) {
            this.weaponSystem = new WeaponSystem(this, gameObjectManager);
        }
        
        // Add player tag
        this.addTag('player');
        
        console.log('PlayerShip created at position:', this.position.toString());
    }
    
    /**
     * Update ship movement and animation
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} movementInput - Normalized movement input vector
     */
    update(deltaTime, movementInput = new Vector2(0, 0)) {
        if (!this.active) return;
        
        // Store movement input for animation
        this.movementInput = movementInput.clone();
        this.isMoving = movementInput.magnitude() > 0.1;
        
        // Apply movement with acceleration
        if (this.isMoving) {
            // Calculate target velocity
            const targetVelocity = movementInput.multiply(this.maxSpeed);
            
            // Apply acceleration towards target velocity
            const velocityDiff = targetVelocity.subtract(this.velocity);
            const accelerationForce = velocityDiff.multiply(this.acceleration * deltaTime / 1000);
            
            this.velocity.addInPlace(accelerationForce);
            
            // Clamp velocity to max speed
            if (this.velocity.magnitude() > this.maxSpeed) {
                this.velocity = this.velocity.normalize().multiply(this.maxSpeed);
            }
        } else {
            // Apply friction when not moving
            this.velocity.multiplyInPlace(Math.pow(this.friction, deltaTime / 16.67));
            
            // Stop very small velocities to prevent jitter
            if (this.velocity.magnitude() < 1) {
                this.velocity.set(0, 0);
            }
        }
        
        // Update position
        this.position.addInPlace(this.velocity.multiply(deltaTime / 1000));
        
        // Apply boundary checking
        this.checkBoundaries();
        
        // Update animation timer
        this.animationTime += deltaTime / 1000;
        
        // Update health and lives timers
        this.updateHealthSystem(deltaTime);
        
        // Update collision bounds
        this.updateCollisionBounds();
        
        // Update weapon system
        if (this.weaponSystem) {
            this.weaponSystem.update(deltaTime);
        }
    }
    
    /**
     * Check and enforce canvas boundaries
     * Keeps the ship fully visible on screen
     */
    checkBoundaries() {
        const leftBound = this.boundaryPadding;
        const rightBound = this.canvasWidth - this.boundaryPadding;
        const topBound = this.boundaryPadding;
        const bottomBound = this.canvasHeight - this.boundaryPadding;
        
        let hitBoundary = false;
        
        // Check horizontal boundaries
        if (this.position.x < leftBound) {
            this.position.x = leftBound;
            this.velocity.x = Math.max(0, this.velocity.x); // Stop leftward velocity
            hitBoundary = true;
        } else if (this.position.x > rightBound) {
            this.position.x = rightBound;
            this.velocity.x = Math.min(0, this.velocity.x); // Stop rightward velocity
            hitBoundary = true;
        }
        
        // Check vertical boundaries
        if (this.position.y < topBound) {
            this.position.y = topBound;
            this.velocity.y = Math.max(0, this.velocity.y); // Stop upward velocity
            hitBoundary = true;
        } else if (this.position.y > bottomBound) {
            this.position.y = bottomBound;
            this.velocity.y = Math.min(0, this.velocity.y); // Stop downward velocity
            hitBoundary = true;
        }
        
        // Optional: Add screen shake or sound effect when hitting boundary
        if (hitBoundary) {
            // Could trigger boundary hit effect here
        }
    }
    
    /**
     * Render the player ship with sprite graphics and animations
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} interpolation - Interpolation factor for smooth rendering
     */
    render(ctx, interpolation = 0) {
        if (!this.visible) return;
        
        // Calculate interpolated position for smooth rendering
        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));
        
        ctx.save();
        ctx.translate(renderPos.x, renderPos.y);
        ctx.rotate(this.rotation);
        
        // Draw ship body
        this.drawShipBody(ctx);
        
        // Draw thruster effects when moving
        if (this.isMoving) {
            this.drawThrusterEffects(ctx);
        }
        
        // Draw collision bounds in debug mode (optional)
        if (window.DEBUG_MODE) {
            this.drawDebugInfo(ctx);
        }
        
        ctx.restore();
        
        // Render weapon effects (muzzle flash, etc.)
        if (this.weaponSystem) {
            this.weaponSystem.render(ctx);
        }
    }
    
    /**
     * Draw the main ship body
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawShipBody(ctx) {
        // Apply visual effects based on ship state
        let shipAlpha = 1.0;
        let shipColor = '#4A90E2';
        let strokeColor = '#2E5C8A';
        
        // Invulnerability effect - flashing
        if (this.isInvulnerable) {
            const flashSpeed = 8; // flashes per second
            const flashPhase = Math.sin(this.invulnerabilityTimer * flashSpeed * Math.PI / 1000);
            shipAlpha = 0.3 + 0.7 * Math.abs(flashPhase);
        }
        
        // Damage flash effect - red tint
        if (this.isFlashing) {
            const flashIntensity = this.damageFlashTimer / this.damageFlashDuration;
            shipColor = this.interpolateColor('#4A90E2', '#FF4444', flashIntensity);
            strokeColor = this.interpolateColor('#2E5C8A', '#CC2222', flashIntensity);
        }
        
        // Apply alpha for transparency effects
        ctx.globalAlpha = shipAlpha;
        
        // Ship body - triangular design
        ctx.fillStyle = shipColor;
        ctx.strokeStyle = strokeColor;
        ctx.lineWidth = 2;
        
        ctx.beginPath();
        // Main body (triangle pointing up)
        ctx.moveTo(0, -this.height / 2); // Top point
        ctx.lineTo(-this.width / 3, this.height / 3); // Bottom left
        ctx.lineTo(this.width / 3, this.height / 3); // Bottom right
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // Ship details - cockpit
        ctx.fillStyle = '#7BB3F0';
        ctx.beginPath();
        ctx.ellipse(0, -this.height / 4, this.width / 6, this.height / 8, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // Wing details
        ctx.fillStyle = '#2E5C8A';
        ctx.fillRect(-this.width / 4, this.height / 6, this.width / 8, this.height / 4);
        ctx.fillRect(this.width / 8, this.height / 6, this.width / 8, this.height / 4);
        
        // Engine glow (subtle)
        ctx.fillStyle = '#87CEEB';
        ctx.beginPath();
        ctx.ellipse(-this.width / 6, this.height / 3, 3, 6, 0, 0, Math.PI * 2);
        ctx.ellipse(this.width / 6, this.height / 3, 3, 6, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // Restore global alpha
        ctx.globalAlpha = 1.0;
    }
    
    /**
     * Draw thruster effects when ship is moving
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawThrusterEffects(ctx) {
        // Animated thruster flames
        const thrusterIntensity = this.velocity.magnitude() / this.maxSpeed;
        const animationPhase = Math.sin(this.animationTime * this.thrusterAnimationSpeed * Math.PI * 2);
        
        // Base flame length based on movement intensity
        const baseFlameLength = 15 * thrusterIntensity;
        const flameVariation = 5 * animationPhase * thrusterIntensity;
        const flameLength = baseFlameLength + flameVariation;
        
        if (flameLength > 2) {
            // Left thruster
            this.drawThrusterFlame(ctx, -this.width / 6, this.height / 3, flameLength);
            
            // Right thruster
            this.drawThrusterFlame(ctx, this.width / 6, this.height / 3, flameLength);
        }
        
        // Additional directional thrusters based on movement
        this.drawDirectionalThrusters(ctx, thrusterIntensity, animationPhase);
    }
    
    /**
     * Draw individual thruster flame
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} x - X position relative to ship center
     * @param {number} y - Y position relative to ship center
     * @param {number} length - Flame length
     */
    drawThrusterFlame(ctx, x, y, length) {
        const gradient = ctx.createLinearGradient(x, y, x, y + length);
        gradient.addColorStop(0, '#FFD700'); // Gold at base
        gradient.addColorStop(0.5, '#FF6B35'); // Orange in middle
        gradient.addColorStop(1, 'rgba(255, 0, 0, 0)'); // Transparent red at tip
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.moveTo(x - 3, y);
        ctx.lineTo(x + 3, y);
        ctx.lineTo(x + 1, y + length);
        ctx.lineTo(x - 1, y + length);
        ctx.closePath();
        ctx.fill();
    }
    
    /**
     * Draw directional thrusters based on movement direction
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} intensity - Movement intensity (0-1)
     * @param {number} animationPhase - Animation phase (-1 to 1)
     */
    drawDirectionalThrusters(ctx, intensity, animationPhase) {
        const thrusterSize = 3 * intensity;
        const alpha = 0.7 * intensity;
        
        // Side thrusters for horizontal movement
        if (Math.abs(this.movementInput.x) > 0.1) {
            ctx.fillStyle = `rgba(135, 206, 235, ${alpha})`;
            
            if (this.movementInput.x > 0) {
                // Moving right, show left thruster
                ctx.fillRect(-this.width / 2 - thrusterSize, -2, thrusterSize, 4);
            } else {
                // Moving left, show right thruster
                ctx.fillRect(this.width / 2, -2, thrusterSize, 4);
            }
        }
        
        // Forward thrusters for upward movement
        if (this.movementInput.y < -0.1) {
            ctx.fillStyle = `rgba(255, 215, 0, ${alpha})`;
            const forwardThrusterLength = 8 * intensity * (1 + 0.3 * animationPhase);
            ctx.fillRect(-2, -this.height / 2 - forwardThrusterLength, 4, forwardThrusterLength);
        }
    }
    
    /**
     * Draw debug information (collision bounds, velocity, etc.)
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawDebugInfo(ctx) {
        // Collision circle
        ctx.strokeStyle = '#FF0000';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(0, 0, this.collisionRadius, 0, Math.PI * 2);
        ctx.stroke();
        
        // Velocity vector
        if (this.velocity.magnitude() > 1) {
            ctx.strokeStyle = '#00FF00';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, 0);
            const velocityScale = 0.1; // Scale down for visibility
            ctx.lineTo(this.velocity.x * velocityScale, this.velocity.y * velocityScale);
            ctx.stroke();
        }
        
        // Ship center point
        ctx.fillStyle = '#FFFF00';
        ctx.fillRect(-1, -1, 2, 2);
    }
    
    /**
     * Get the ship's current speed
     * @returns {number} Current speed in pixels per second
     */
    getCurrentSpeed() {
        return this.velocity.magnitude();
    }
    
    /**
     * Check if ship is at the boundary
     * @returns {object} Object indicating which boundaries are being touched
     */
    getBoundaryStatus() {
        const leftBound = this.boundaryPadding;
        const rightBound = this.canvasWidth - this.boundaryPadding;
        const topBound = this.boundaryPadding;
        const bottomBound = this.canvasHeight - this.boundaryPadding;
        
        return {
            left: this.position.x <= leftBound,
            right: this.position.x >= rightBound,
            top: this.position.y <= topBound,
            bottom: this.position.y >= bottomBound
        };
    }
    
    /**
     * Reset ship to starting position and state
     * @param {number} x - Starting X position
     * @param {number} y - Starting Y position
     */
    resetToPosition(x, y) {
        this.position.set(x, y);
        this.velocity.set(0, 0);
        this.rotation = 0;
        this.animationTime = 0;
        this.isMoving = false;
        this.movementInput.set(0, 0);
        this.active = true;
        this.visible = true;
        this.destroyed = false;
        
        // Don't reset health/lives here - that's handled by respawn() or resetHealthAndLives()
    }
    
    /**
     * Update canvas dimensions (for window resize)
     * @param {number} width - New canvas width
     * @param {number} height - New canvas height
     */
    updateCanvasDimensions(width, height) {
        this.canvasWidth = width;
        this.canvasHeight = height;
        
        // Re-check boundaries with new dimensions
        this.checkBoundaries();
    }
    
    /**
     * Fire weapon in specified direction
     * @param {Vector2} direction - Direction to fire (optional, defaults to up)
     * @returns {boolean} True if weapon fired successfully
     */
    fire(direction = Vector2.up()) {
        console.log('PlayerShip.fire() called, weaponSystem exists:', !!this.weaponSystem);
        if (this.weaponSystem) {
            const result = this.weaponSystem.fire(direction);
            console.log('WeaponSystem.fire() returned:', result);
            return result;
        }
        return false;
    }
    
    /**
     * Set weapon system (for initialization after construction)
     * @param {WeaponSystem} weaponSystem - Weapon system instance
     */
    setWeaponSystem(weaponSystem) {
        this.weaponSystem = weaponSystem;
    }
    
    /**
     * Get weapon system
     * @returns {WeaponSystem|null} Current weapon system
     */
    getWeaponSystem() {
        return this.weaponSystem;
    }
    
    /**
     * Check if weapon is ready to fire
     * @returns {boolean} True if weapon can fire
     */
    canFire() {
        return this.weaponSystem ? this.weaponSystem.isReady() : false;
    }
    
    /**
     * Set weapon pattern
     * @param {string} pattern - Weapon pattern ('single', 'double', 'triple', 'spread')
     */
    setWeaponPattern(pattern) {
        if (this.weaponSystem) {
            this.weaponSystem.setPattern(pattern);
        }
    }
    
    /**
     * Get weapon statistics
     * @returns {object|null} Weapon stats or null if no weapon system
     */
    getWeaponStats() {
        return this.weaponSystem ? this.weaponSystem.getStats() : null;
    }
    
    /**
     * Update health system timers (invulnerability and damage flash)
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    updateHealthSystem(deltaTime) {
        // Update invulnerability timer
        if (this.isInvulnerable) {
            this.invulnerabilityTimer -= deltaTime;
            if (this.invulnerabilityTimer <= 0) {
                this.isInvulnerable = false;
                this.invulnerabilityTimer = 0;
            }
        }
        
        // Update damage flash timer
        if (this.isFlashing) {
            this.damageFlashTimer -= deltaTime;
            if (this.damageFlashTimer <= 0) {
                this.isFlashing = false;
                this.damageFlashTimer = 0;
            }
        }
    }
    
    /**
     * Take damage and handle health/lives logic
     * @param {number} damage - Amount of damage to take
     * @returns {object} Result object with damage taken, health remaining, lives remaining, and destroyed status
     */
    takeDamage(damage) {
        // Can't take damage if invulnerable or already destroyed
        if (this.isInvulnerable || this.isDestroyed) {
            return {
                damageTaken: 0,
                health: this.health,
                lives: this.lives,
                destroyed: this.isDestroyed
            };
        }
        
        // Apply damage
        const actualDamage = Math.min(damage, this.health);
        this.health -= actualDamage;
        
        // Trigger damage flash effect
        this.isFlashing = true;
        this.damageFlashTimer = this.damageFlashDuration;
        
        console.log(`PlayerShip took ${actualDamage} damage. Health: ${this.health}/${this.maxHealth}, Lives: ${this.lives}`);
        
        // Check if ship is destroyed
        if (this.health <= 0) {
            this.destroyShip();
        } else {
            // Grant brief invulnerability after taking damage
            this.isInvulnerable = true;
            this.invulnerabilityTimer = this.invulnerabilityDuration;
        }
        
        return {
            damageTaken: actualDamage,
            health: this.health,
            lives: this.lives,
            destroyed: this.isDestroyed
        };
    }
    
    /**
     * Destroy the ship and handle respawn logic
     */
    destroyShip() {
        this.health = 0;
        this.lives--;
        
        console.log(`PlayerShip destroyed! Lives remaining: ${this.lives}`);
        
        if (this.lives <= 0) {
            // Game over - no more lives
            this.isDestroyed = true;
            this.active = false;
            console.log('Game Over - No lives remaining');
        } else {
            // Respawn with full health and invulnerability
            this.respawn();
        }
    }
    
    /**
     * Respawn the ship with full health and temporary invulnerability
     */
    respawn() {
        // Reset health
        this.health = this.maxHealth;
        
        // Reset position to starting location
        const startX = this.canvasWidth / 2;
        const startY = this.canvasHeight - 100;
        this.resetToPosition(startX, startY);
        
        // Grant extended invulnerability after respawn
        this.isInvulnerable = true;
        this.invulnerabilityTimer = this.invulnerabilityDuration * 2; // Double duration after respawn
        
        // Reset damage flash
        this.isFlashing = false;
        this.damageFlashTimer = 0;
        
        console.log(`PlayerShip respawned with full health. Lives: ${this.lives}`);
    }
    
    /**
     * Heal the ship by a specified amount
     * @param {number} healAmount - Amount of health to restore
     * @returns {number} Actual amount healed
     */
    heal(healAmount) {
        if (this.isDestroyed) return 0;
        
        const actualHeal = Math.min(healAmount, this.maxHealth - this.health);
        this.health += actualHeal;
        
        console.log(`PlayerShip healed for ${actualHeal}. Health: ${this.health}/${this.maxHealth}`);
        return actualHeal;
    }
    
    /**
     * Add extra lives
     * @param {number} extraLives - Number of lives to add
     */
    addLives(extraLives) {
        this.lives += extraLives;
        console.log(`PlayerShip gained ${extraLives} lives. Total lives: ${this.lives}`);
    }
    
    /**
     * Get current health status
     * @returns {object} Health status object
     */
    getHealthStatus() {
        return {
            health: this.health,
            maxHealth: this.maxHealth,
            healthPercentage: this.health / this.maxHealth,
            lives: this.lives,
            maxLives: this.maxLives,
            isInvulnerable: this.isInvulnerable,
            invulnerabilityTimeRemaining: this.invulnerabilityTimer,
            isDestroyed: this.isDestroyed,
            isFlashing: this.isFlashing
        };
    }
    
    /**
     * Reset health and lives to maximum (for new game)
     */
    resetHealthAndLives() {
        this.health = this.maxHealth;
        this.lives = this.maxLives;
        this.isInvulnerable = false;
        this.invulnerabilityTimer = 0;
        this.isDestroyed = false;
        this.isFlashing = false;
        this.damageFlashTimer = 0;
        
        console.log('PlayerShip health and lives reset to maximum');
    }
    
    /**
     * Interpolate between two hex colors
     * @param {string} color1 - Starting color (hex format)
     * @param {string} color2 - Ending color (hex format)
     * @param {number} factor - Interpolation factor (0-1)
     * @returns {string} Interpolated color in hex format
     */
    interpolateColor(color1, color2, factor) {
        // Clamp factor between 0 and 1
        factor = Math.max(0, Math.min(1, factor));
        
        // Parse hex colors
        const hex1 = color1.replace('#', '');
        const hex2 = color2.replace('#', '');
        
        const r1 = parseInt(hex1.substr(0, 2), 16);
        const g1 = parseInt(hex1.substr(2, 2), 16);
        const b1 = parseInt(hex1.substr(4, 2), 16);
        
        const r2 = parseInt(hex2.substr(0, 2), 16);
        const g2 = parseInt(hex2.substr(2, 2), 16);
        const b2 = parseInt(hex2.substr(4, 2), 16);
        
        // Interpolate each channel
        const r = Math.round(r1 + (r2 - r1) * factor);
        const g = Math.round(g1 + (g2 - g1) * factor);
        const b = Math.round(b1 + (b2 - b1) * factor);
        
        // Convert back to hex
        const toHex = (n) => {
            const hex = n.toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };
        
        return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    }
}