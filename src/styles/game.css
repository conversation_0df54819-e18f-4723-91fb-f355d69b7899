body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    background: linear-gradient(45deg, #1a0537, #000020, #2d1a05);
    overflow: hidden;
}

#game-container {
    position: relative;
    width: 100vw;
    height: calc(100vw * 16 / 9);
    max-height: 100vh;
    max-width: calc(100vh * 9 / 16);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

/* Game Canvas Styles */
#gameCanvas {
  width: 100%;
  height: 100%;
  display: block;
  background: #000;
  border: 2px solid #00ffff;
}

/* Game UI Styles */
.game-ui {
  position: fixed;
  top: 10px;
  left: 10px;
  color: white;
  font-family: Arial, sans-serif;
  z-index: 100;
}

.game-stats {
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #00ffff;
}

.game-stats p {
  margin: 5px 0;
  font-size: 14px;
}

/* Health bar */
.health-bar {
  width: 100px;
  height: 10px;
  background: #333;
  border: 1px solid #fff;
  margin-top: 5px;
}

.health-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff0000, #ffff00, #00ff00);
  transition: width 0.3s ease;
}

/* Game over screen */
.game-over {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.game-over-content {
  text-align: center;
  color: white;
  font-family: Arial, sans-serif;
}

.game-over h1 {
  font-size: 3rem;
  color: #ff4444;
  margin-bottom: 20px;
}

.game-over button {
  background: #00ffff;
  color: #000;
  border: none;
  padding: 15px 30px;
  font-size: 18px;
  border-radius: 5px;
  cursor: pointer;
  margin: 10px;
}

.game-over button:hover {
  background: #00cccc;
}
