.main-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  font-family: 'Arial', sans-serif;
}

.menu-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a0a2e 0%, #16213e 50%, #0f3460 100%);
  position: relative;
  overflow: hidden;
}

.stars {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, #eee, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: twinkle 4s ease-in-out infinite alternate;
}

@keyframes twinkle {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

.menu-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;
}

.game-title {
  font-size: 4rem;
  font-weight: bold;
  color: #00ffff;
  text-shadow: 0 0 20px #00ffff, 0 0 40px #00ffff;
  margin-bottom: 10px;
  letter-spacing: 4px;
}

.game-subtitle {
  font-size: 1.2rem;
  color: #ffffff;
  margin-bottom: 40px;
  opacity: 0.8;
}

.auth-section {
  margin-bottom: 30px;
  min-height: 100px;
}

.orange-id-container {
  margin-bottom: 20px;
}

.debug-section {
  margin-top: 20px;
}

.debug-button {
  background: #ff6b35;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.debug-button:hover {
  background: #e55a2b;
}

.menu-button {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  margin: 10px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  min-width: 200px;
}

.menu-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.menu-button.primary {
  background: linear-gradient(45deg, #00ffff 0%, #0080ff 100%);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.menu-button.secondary {
  background: linear-gradient(45deg, #666 0%, #999 100%);
  font-size: 14px;
  padding: 10px 20px;
  min-width: 150px;
}

.user-info {
  margin-top: 20px;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 25px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.user-info p {
  margin: 5px 0;
}

.error-message {
  color: #ff4444;
  background: rgba(255, 68, 68, 0.1);
  padding: 10px 20px;
  border-radius: 5px;
  margin-top: 20px;
  border: 1px solid #ff4444;
}

/* Orange ID widget styling */
#bedrock-login-widget {
  min-height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .game-title {
    font-size: 2.5rem;
  }
  
  .menu-button {
    min-width: 250px;
    padding: 12px 25px;
  }
  
  .menu-content {
    padding: 10px;
  }
}