/**
 * Game math utilities
 */
export class GameMath {
    // Clamp value between min and max
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }
    
    // Linear interpolation
    static lerp(start, end, t) {
        return start + (end - start) * t;
    }
    
    // Check if point is within rectangle bounds
    static pointInRect(point, rect) {
        return point.x >= rect.x && 
               point.x <= rect.x + rect.width &&
               point.y >= rect.y && 
               point.y <= rect.y + rect.height;
    }
    
    // Check collision between two rectangles
    static rectCollision(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }
    
    // Check collision between two circles
    static circleCollision(circle1, circle2) {
        const dx = circle1.x - circle2.x;
        const dy = circle1.y - circle2.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        return distance < circle1.radius + circle2.radius;
    }
    
    // Random number between min and max
    static random(min, max) {
        return Math.random() * (max - min) + min;
    }
    
    // Random integer between min and max (inclusive)
    static randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    
    // Convert degrees to radians
    static degToRad(degrees) {
        return degrees * (Math.PI / 180);
    }
    
    // Convert radians to degrees
    static radToDeg(radians) {
        return radians * (180 / Math.PI);
    }
    
    // Advanced collision detection
    static circleRectCollision(circle, rect) {
        // Find the closest point on the rectangle to the circle center
        const closestX = GameMath.clamp(circle.x, rect.x, rect.x + rect.width);
        const closestY = GameMath.clamp(circle.y, rect.y, rect.y + rect.height);
        
        // Calculate distance from circle center to closest point
        const dx = circle.x - closestX;
        const dy = circle.y - closestY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        return distance < circle.radius;
    }
    
    // Line-circle intersection
    static lineCircleCollision(lineStart, lineEnd, circle) {
        const dx = lineEnd.x - lineStart.x;
        const dy = lineEnd.y - lineStart.y;
        const fx = lineStart.x - circle.x;
        const fy = lineStart.y - circle.y;
        
        const a = dx * dx + dy * dy;
        const b = 2 * (fx * dx + fy * dy);
        const c = (fx * fx + fy * fy) - circle.radius * circle.radius;
        
        const discriminant = b * b - 4 * a * c;
        
        if (discriminant < 0) return false;
        
        const discriminantSqrt = Math.sqrt(discriminant);
        const t1 = (-b - discriminantSqrt) / (2 * a);
        const t2 = (-b + discriminantSqrt) / (2 * a);
        
        return (t1 >= 0 && t1 <= 1) || (t2 >= 0 && t2 <= 1);
    }
    
    // Smooth step interpolation
    static smoothStep(edge0, edge1, x) {
        const t = GameMath.clamp((x - edge0) / (edge1 - edge0), 0, 1);
        return t * t * (3 - 2 * t);
    }
    
    // Ease in/out functions
    static easeInQuad(t) {
        return t * t;
    }
    
    static easeOutQuad(t) {
        return t * (2 - t);
    }
    
    static easeInOutQuad(t) {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    }
    
    // Wrap angle to [-PI, PI]
    static wrapAngle(angle) {
        while (angle > Math.PI) angle -= 2 * Math.PI;
        while (angle < -Math.PI) angle += 2 * Math.PI;
        return angle;
    }
    
    // Angle difference (shortest path)
    static angleDifference(angle1, angle2) {
        let diff = angle2 - angle1;
        return GameMath.wrapAngle(diff);
    }
    
    // Move towards target with maximum step
    static moveTowards(current, target, maxStep) {
        const diff = target - current;
        if (Math.abs(diff) <= maxStep) {
            return target;
        }
        return current + Math.sign(diff) * maxStep;
    }
    
    // Check if value is approximately equal (with tolerance)
    static approximately(a, b, tolerance = 0.0001) {
        return Math.abs(a - b) < tolerance;
    }
    
    // Map value from one range to another
    static map(value, fromMin, fromMax, toMin, toMax) {
        return (value - fromMin) * (toMax - toMin) / (fromMax - fromMin) + toMin;
    }
}