import { Vector2 } from './Vector2.js';

/**
 * Base GameObject class for all game entities
 * Provides common functionality for position, movement, and lifecycle
 */
export class GameObject {
    constructor(x = 0, y = 0) {
        this.position = new Vector2(x, y);
        this.velocity = new Vector2(0, 0);
        this.acceleration = new Vector2(0, 0);
        
        // Transform properties
        this.rotation = 0;
        this.scale = new Vector2(1, 1);
        
        // State properties
        this.active = true;
        this.visible = true;
        this.destroyed = false;
        
        // Collision properties
        this.collisionRadius = 0;
        this.collisionBounds = { x: 0, y: 0, width: 0, height: 0 };
        
        // Unique identifier
        this.id = GameObject.generateId();
        
        // Tags for categorization
        this.tags = new Set();
    }
    
    // Static ID generator
    static idCounter = 0;
    static generateId() {
        return ++GameObject.idCounter;
    }
    
    // Update method - override in subclasses
    update(deltaTime) {
        if (!this.active) return;
        
        // Apply physics
        this.velocity.addInPlace(this.acceleration.multiply(deltaTime / 1000));
        this.position.addInPlace(this.velocity.multiply(deltaTime / 1000));
        
        // Update collision bounds
        this.updateCollisionBounds();
    }
    
    // Render method - override in subclasses
    render(ctx, interpolation = 0) {
        if (!this.visible) return;
        
        // Interpolated position for smooth rendering
        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));
        
        // Basic debug rendering (override in subclasses)
        ctx.save();
        ctx.translate(renderPos.x, renderPos.y);
        ctx.rotate(this.rotation);
        ctx.scale(this.scale.x, this.scale.y);
        
        // Draw collision bounds for debugging
        if (this.collisionRadius > 0) {
            ctx.strokeStyle = '#ff0000';
            ctx.beginPath();
            ctx.arc(0, 0, this.collisionRadius, 0, Math.PI * 2);
            ctx.stroke();
        }
        
        ctx.restore();
    }
    
    // Collision detection methods
    updateCollisionBounds() {
        this.collisionBounds.x = this.position.x - this.collisionRadius;
        this.collisionBounds.y = this.position.y - this.collisionRadius;
        this.collisionBounds.width = this.collisionRadius * 2;
        this.collisionBounds.height = this.collisionRadius * 2;
    }
    
    // Check collision with another GameObject
    collidesWith(other) {
        if (!this.active || !other.active) return false;
        
        // Circle collision detection
        if (this.collisionRadius > 0 && other.collisionRadius > 0) {
            const distance = this.position.distance(other.position);
            return distance < (this.collisionRadius + other.collisionRadius);
        }
        
        return false;
    }
    
    // Lifecycle methods
    destroy() {
        this.destroyed = true;
        this.active = false;
        this.visible = false;
    }
    
    reset() {
        this.position.set(0, 0);
        this.velocity.set(0, 0);
        this.acceleration.set(0, 0);
        this.rotation = 0;
        this.scale.set(1, 1);
        this.active = true;
        this.visible = true;
        this.destroyed = false;
        this.tags.clear();
    }
    
    // Tag management
    addTag(tag) {
        this.tags.add(tag);
    }
    
    removeTag(tag) {
        this.tags.delete(tag);
    }
    
    hasTag(tag) {
        return this.tags.has(tag);
    }
    
    // Utility methods
    distanceTo(other) {
        return this.position.distance(other.position);
    }
    
    directionTo(other) {
        return other.position.subtract(this.position).normalize();
    }
    
    lookAt(target) {
        const direction = this.directionTo(target);
        this.rotation = direction.angle();
    }
    
    // Movement helpers
    moveTowards(target, speed, deltaTime) {
        const direction = this.directionTo(target);
        const movement = direction.multiply(speed * deltaTime / 1000);
        this.position.addInPlace(movement);
    }
    
    applyForce(force) {
        this.acceleration.addInPlace(force);
    }
    
    // Boundary checking
    isOutOfBounds(bounds) {
        return this.position.x < bounds.left || 
               this.position.x > bounds.right ||
               this.position.y < bounds.top || 
               this.position.y > bounds.bottom;
    }
    
    wrapAroundBounds(bounds) {
        if (this.position.x < bounds.left) this.position.x = bounds.right;
        if (this.position.x > bounds.right) this.position.x = bounds.left;
        if (this.position.y < bounds.top) this.position.y = bounds.bottom;
        if (this.position.y > bounds.bottom) this.position.y = bounds.top;
    }
    
    clampToBounds(bounds) {
        this.position.x = Math.max(bounds.left, Math.min(bounds.right, this.position.x));
        this.position.y = Math.max(bounds.top, Math.min(bounds.bottom, this.position.y));
    }
}