import { ObjectPool } from './ObjectPool.js';

/**
 * GameObjectManager - Manages collections of game objects
 * Provides efficient update, render, and collision detection
 */
export class GameObjectManager {
    constructor() {
        this.objects = [];
        this.objectsToAdd = [];
        this.objectsToRemove = [];
        this.pools = new Map();
    }
    
    // Add object to manager
    add(object) {
        this.objectsToAdd.push(object);
    }
    
    // Remove object from manager
    remove(object) {
        this.objectsToRemove.push(object);
    }
    
    // Create object pool for a specific type
    createPool(type, createFn, resetFn, initialSize = 10) {
        this.pools.set(type, new ObjectPool(createFn, resetFn, initialSize));
    }
    
    // Get object from pool
    getFromPool(type) {
        const pool = this.pools.get(type);
        if (pool) {
            return pool.get();
        }
        throw new Error(`Pool for type '${type}' not found`);
    }
    
    // Return object to pool
    returnToPool(type, object) {
        const pool = this.pools.get(type);
        if (pool) {
            pool.release(object);
            this.remove(object);
        }
    }
    
    // Update all objects
    update(deltaTime) {
        // Process additions and removals
        this.processAdditions();
        this.processRemovals();
        
        // Update all active objects
        for (let i = this.objects.length - 1; i >= 0; i--) {
            const object = this.objects[i];
            
            if (object.destroyed) {
                this.objectsToRemove.push(object);
                continue;
            }
            
            if (object.active) {
                object.update(deltaTime);
            }
        }
    }
    
    // Render all objects
    render(ctx, interpolation = 0) {
        for (const object of this.objects) {
            if (object.visible && !object.destroyed) {
                object.render(ctx, interpolation);
            }
        }
    }
    
    // Process pending additions
    processAdditions() {
        if (this.objectsToAdd.length > 0) {
            this.objects.push(...this.objectsToAdd);
            this.objectsToAdd.length = 0;
        }
    }
    
    // Process pending removals
    processRemovals() {
        if (this.objectsToRemove.length > 0) {
            for (const objectToRemove of this.objectsToRemove) {
                const index = this.objects.indexOf(objectToRemove);
                if (index !== -1) {
                    this.objects.splice(index, 1);
                }
            }
            this.objectsToRemove.length = 0;
        }
    }
    
    // Find objects by tag
    findByTag(tag) {
        return this.objects.filter(obj => obj.hasTag(tag));
    }
    
    // Find object by ID
    findById(id) {
        return this.objects.find(obj => obj.id === id);
    }
    
    // Get all active objects
    getActive() {
        return this.objects.filter(obj => obj.active && !obj.destroyed);
    }
    
    // Get all visible objects
    getVisible() {
        return this.objects.filter(obj => obj.visible && !obj.destroyed);
    }
    
    // Collision detection between groups
    checkCollisions(group1Tag, group2Tag, callback) {
        const group1 = this.findByTag(group1Tag);
        const group2 = this.findByTag(group2Tag);
        
        for (const obj1 of group1) {
            if (!obj1.active) continue;
            
            for (const obj2 of group2) {
                if (!obj2.active) continue;
                
                if (obj1.collidesWith(obj2)) {
                    callback(obj1, obj2);
                }
            }
        }
    }
    
    // Broad phase collision detection using spatial partitioning
    checkCollisionsOptimized(group1Tag, group2Tag, callback, cellSize = 64) {
        const group1 = this.findByTag(group1Tag);
        const group2 = this.findByTag(group2Tag);
        
        // Simple spatial hash for broad phase
        const spatialHash = new Map();
        
        // Hash group2 objects
        for (const obj of group2) {
            if (!obj.active) continue;
            
            const cellX = Math.floor(obj.position.x / cellSize);
            const cellY = Math.floor(obj.position.y / cellSize);
            const key = `${cellX},${cellY}`;
            
            if (!spatialHash.has(key)) {
                spatialHash.set(key, []);
            }
            spatialHash.get(key).push(obj);
        }
        
        // Check group1 objects against nearby cells
        for (const obj1 of group1) {
            if (!obj1.active) continue;
            
            const cellX = Math.floor(obj1.position.x / cellSize);
            const cellY = Math.floor(obj1.position.y / cellSize);
            
            // Check surrounding cells
            for (let dx = -1; dx <= 1; dx++) {
                for (let dy = -1; dy <= 1; dy++) {
                    const key = `${cellX + dx},${cellY + dy}`;
                    const nearbyObjects = spatialHash.get(key);
                    
                    if (nearbyObjects) {
                        for (const obj2 of nearbyObjects) {
                            if (obj1.collidesWith(obj2)) {
                                callback(obj1, obj2);
                            }
                        }
                    }
                }
            }
        }
    }
    
    // Clear all objects
    clear() {
        this.objects.length = 0;
        this.objectsToAdd.length = 0;
        this.objectsToRemove.length = 0;
        
        // Clear all pools
        for (const pool of this.pools.values()) {
            pool.releaseAll();
        }
    }
    
    // Get statistics
    getStats() {
        const poolStats = {};
        for (const [type, pool] of this.pools.entries()) {
            poolStats[type] = pool.getStats();
        }
        
        return {
            totalObjects: this.objects.length,
            activeObjects: this.getActive().length,
            visibleObjects: this.getVisible().length,
            pendingAdditions: this.objectsToAdd.length,
            pendingRemovals: this.objectsToRemove.length,
            pools: poolStats
        };
    }
}