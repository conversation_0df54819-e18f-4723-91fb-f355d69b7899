/**
 * Vector2 utility class for 2D math operations
 */
export class Vector2 {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }
    
    // Static factory methods
    static zero() {
        return new Vector2(0, 0);
    }
    
    static one() {
        return new Vector2(1, 1);
    }
    
    static up() {
        return new Vector2(0, -1);
    }
    
    static down() {
        return new Vector2(0, 1);
    }
    
    static left() {
        return new Vector2(-1, 0);
    }
    
    static right() {
        return new Vector2(1, 0);
    }
    
    // Vector operations
    add(other) {
        return new Vector2(this.x + other.x, this.y + other.y);
    }
    
    subtract(other) {
        return new Vector2(this.x - other.x, this.y - other.y);
    }
    
    multiply(scalar) {
        return new Vector2(this.x * scalar, this.y * scalar);
    }
    
    divide(scalar) {
        if (scalar === 0) throw new Error('Division by zero');
        return new Vector2(this.x / scalar, this.y / scalar);
    }
    
    // Magnitude and normalization
    magnitude() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }
    
    normalize() {
        const mag = this.magnitude();
        if (mag === 0) return Vector2.zero();
        return this.divide(mag);
    }
    
    // Distance calculations
    distance(other) {
        return this.subtract(other).magnitude();
    }
    
    // Dot product
    dot(other) {
        return this.x * other.x + this.y * other.y;
    }
    
    // In-place operations (modify this vector)
    addInPlace(other) {
        this.x += other.x;
        this.y += other.y;
        return this;
    }
    
    subtractInPlace(other) {
        this.x -= other.x;
        this.y -= other.y;
        return this;
    }
    
    multiplyInPlace(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        return this;
    }
    
    normalizeInPlace() {
        const mag = this.magnitude();
        if (mag > 0) {
            this.x /= mag;
            this.y /= mag;
        }
        return this;
    }
    
    // Set values
    set(x, y) {
        this.x = x;
        this.y = y;
        return this;
    }
    
    setFromVector(other) {
        this.x = other.x;
        this.y = other.y;
        return this;
    }
    
    // Angle operations
    angle() {
        return Math.atan2(this.y, this.x);
    }
    
    static fromAngle(angle, magnitude = 1) {
        return new Vector2(
            Math.cos(angle) * magnitude,
            Math.sin(angle) * magnitude
        );
    }
    
    // Rotation
    rotate(angle) {
        const cos = Math.cos(angle);
        const sin = Math.sin(angle);
        const newX = this.x * cos - this.y * sin;
        const newY = this.x * sin + this.y * cos;
        return new Vector2(newX, newY);
    }
    
    rotateInPlace(angle) {
        const cos = Math.cos(angle);
        const sin = Math.sin(angle);
        const newX = this.x * cos - this.y * sin;
        const newY = this.x * sin + this.y * cos;
        this.x = newX;
        this.y = newY;
        return this;
    }
    
    // Perpendicular vector
    perpendicular() {
        return new Vector2(-this.y, this.x);
    }
    
    // Utility methods
    clone() {
        return new Vector2(this.x, this.y);
    }
    
    equals(other, tolerance = 0) {
        if (tolerance === 0) {
            return this.x === other.x && this.y === other.y;
        }
        return Math.abs(this.x - other.x) <= tolerance && 
               Math.abs(this.y - other.y) <= tolerance;
    }
    
    toString() {
        return `Vector2(${this.x.toFixed(2)}, ${this.y.toFixed(2)})`;
    }
}