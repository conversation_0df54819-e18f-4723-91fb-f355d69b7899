import { GAME_CONFIG } from '../config/gameConfig.js';

/**
 * TokenEconomyManager handles WISH token tracking, transactions, and rewards
 * Implements performance-based token calculation and balance management
 */
export class TokenEconomyManager {
    constructor() {
        // Token balance
        this.playerBalance = 0;
        this.totalEarned = 0;
        this.totalSpent = 0;
        
        // Transaction history
        this.transactionHistory = [];
        this.maxHistorySize = 100;
        
        // Performance tracking for rewards
        this.performanceMetrics = {
            levelsCompleted: 0,
            totalScore: 0,
            averageCompletionTime: 0,
            perfectCompletions: 0,
            speedBonuses: 0,
            accuracyBonuses: 0
        };
        
        // Token reward multipliers
        this.rewardMultipliers = {
            base: 1.0,
            speed: 1.5,
            accuracy: 1.3,
            perfect: 2.0,
            difficulty: 1.0
        };
        
        // Visual feedback state
        this.pendingRewards = [];
        this.rewardAnimations = [];
        
        // Callbacks for UI updates
        this.onBalanceUpdateCallback = null;
        this.onTransactionCallback = null;
        this.onRewardEarnedCallback = null;
        
        console.log('TokenEconomyManager initialized');
    }
    
    /**
     * Calculate token reward based on level completion performance
     * @param {object} completionData - Level completion data from LevelManager
     * @returns {object} Token reward breakdown
     */
    calculateLevelReward(completionData) {
        if (!completionData.completed) {
            return { totalReward: 0, breakdown: { reason: 'level_not_completed' } };
        }
        
        const levelNumber = completionData.levelNumber;
        const completionTime = completionData.completionTime;
        const scoreData = completionData.score;
        
        // Base reward calculation
        const baseReward = this.calculateBaseReward(levelNumber);
        
        // Time-based bonus (inverse relationship - faster = more tokens)
        const timeBonus = this.calculateTimeBonus(completionTime, levelNumber);
        
        // Score-based bonus
        const scoreBonus = this.calculateScoreBonus(scoreData.totalScore, levelNumber);
        
        // Performance bonuses
        const speedBonus = completionData.bonuses.speed ? this.calculateSpeedBonus(baseReward) : 0;
        const accuracyBonus = completionData.bonuses.accuracy ? this.calculateAccuracyBonus(baseReward) : 0;
        const perfectBonus = completionData.bonuses.perfect ? this.calculatePerfectBonus(baseReward) : 0;
        
        // Difficulty multiplier
        const difficultyMultiplier = this.calculateDifficultyMultiplier(levelNumber);
        
        // Calculate total before multiplier
        const subtotal = baseReward + timeBonus + scoreBonus + speedBonus + accuracyBonus + perfectBonus;
        
        // Apply difficulty multiplier
        const totalReward = Math.floor(subtotal * difficultyMultiplier);
        
        const breakdown = {
            baseReward,
            timeBonus,
            scoreBonus,
            speedBonus,
            accuracyBonus,
            perfectBonus,
            difficultyMultiplier,
            subtotal,
            totalReward
        };
        
        console.log(`Level ${levelNumber} token reward calculated:`, breakdown);
        
        return { totalReward, breakdown };
    }
    
    /**
     * Calculate base reward for level completion
     * @param {number} levelNumber - Level number
     * @returns {number} Base reward amount
     */
    calculateBaseReward(levelNumber) {
        const baseAmount = GAME_CONFIG.BASE_LEVEL_REWARD;
        const levelMultiplier = Math.floor((levelNumber - 1) / 5) + 1; // Increases every 5 levels
        return baseAmount * levelMultiplier;
    }
    
    /**
     * Calculate time bonus (faster completion = more tokens)
     * @param {number} completionTime - Time taken in seconds
     * @param {number} levelNumber - Level number
     * @returns {number} Time bonus amount
     */
    calculateTimeBonus(completionTime, levelNumber) {
        // Expected completion time increases with level
        const expectedTime = 60 + (levelNumber * 10); // Base 60s + 10s per level
        const fastTime = expectedTime * 0.5; // 50% of expected time for max bonus
        
        if (completionTime <= fastTime) {
            // Excellent time - maximum bonus
            return Math.floor(30 + (levelNumber * 5));
        } else if (completionTime <= expectedTime * 0.75) {
            // Good time - partial bonus
            return Math.floor(20 + (levelNumber * 3));
        } else if (completionTime <= expectedTime) {
            // Acceptable time - small bonus
            return Math.floor(10 + levelNumber);
        }
        
        return 0; // No bonus for slow completion
    }
    
    /**
     * Calculate score bonus based on points earned
     * @param {number} score - Score achieved
     * @param {number} levelNumber - Level number
     * @returns {number} Score bonus amount
     */
    calculateScoreBonus(score, levelNumber) {
        // Score bonus is a percentage of the score, scaled by level
        const basePercentage = 0.01; // 1% of score as base
        const levelScaling = Math.min(2.0, 1.0 + (levelNumber * 0.05)); // Up to 2x scaling
        
        return Math.floor(score * basePercentage * levelScaling);
    }
    
    /**
     * Calculate speed bonus for fast completion
     * @param {number} baseReward - Base reward amount
     * @returns {number} Speed bonus amount
     */
    calculateSpeedBonus(baseReward) {
        return Math.floor(baseReward * (this.rewardMultipliers.speed - 1.0));
    }
    
    /**
     * Calculate accuracy bonus for high accuracy
     * @param {number} baseReward - Base reward amount
     * @returns {number} Accuracy bonus amount
     */
    calculateAccuracyBonus(baseReward) {
        return Math.floor(baseReward * (this.rewardMultipliers.accuracy - 1.0));
    }
    
    /**
     * Calculate perfect completion bonus
     * @param {number} baseReward - Base reward amount
     * @returns {number} Perfect bonus amount
     */
    calculatePerfectBonus(baseReward) {
        return Math.floor(baseReward * (this.rewardMultipliers.perfect - 1.0));
    }
    
    /**
     * Calculate difficulty multiplier based on level
     * @param {number} levelNumber - Level number
     * @returns {number} Difficulty multiplier
     */
    calculateDifficultyMultiplier(levelNumber) {
        // Multiplier increases gradually with level
        const baseMultiplier = 1.0;
        const increment = 0.1;
        const maxMultiplier = 3.0;
        
        const multiplier = baseMultiplier + (Math.floor((levelNumber - 1) / 10) * increment);
        return Math.min(maxMultiplier, multiplier);
    }
    
    /**
     * Award tokens to player balance
     * @param {number} amount - Amount to award
     * @param {string} reason - Reason for the award
     * @param {object} metadata - Additional metadata
     * @returns {object} Transaction result
     */
    awardTokens(amount, reason, metadata = {}) {
        if (amount <= 0) {
            console.warn('Attempted to award non-positive token amount:', amount);
            return { success: false, reason: 'invalid_amount' };
        }
        
        // Update balance
        this.playerBalance += amount;
        this.totalEarned += amount;
        
        // Create transaction record
        const transaction = {
            id: this.generateTransactionId(),
            type: 'earned',
            amount: amount,
            reason: reason,
            timestamp: Date.now(),
            balanceAfter: this.playerBalance,
            metadata: metadata
        };
        
        // Add to history
        this.addTransaction(transaction);
        
        // Update performance metrics
        this.updatePerformanceMetrics(reason, metadata);
        
        // Add to pending rewards for visual feedback
        this.addPendingReward(amount, reason);
        
        console.log(`Awarded ${amount} WISH tokens for ${reason}. New balance: ${this.playerBalance}`);
        
        // Trigger callbacks
        this.triggerBalanceUpdate();
        this.triggerRewardEarned(amount, reason, metadata);
        
        return { 
            success: true, 
            transaction: transaction,
            newBalance: this.playerBalance
        };
    }
    
    /**
     * Spend tokens from player balance
     * @param {number} amount - Amount to spend
     * @param {string} reason - Reason for spending
     * @param {object} metadata - Additional metadata
     * @returns {object} Transaction result
     */
    spendTokens(amount, reason, metadata = {}) {
        if (amount <= 0) {
            console.warn('Attempted to spend non-positive token amount:', amount);
            return { success: false, reason: 'invalid_amount' };
        }
        
        if (this.playerBalance < amount) {
            console.warn(`Insufficient tokens. Required: ${amount}, Available: ${this.playerBalance}`);
            return { success: false, reason: 'insufficient_balance', required: amount, available: this.playerBalance };
        }
        
        // Update balance
        this.playerBalance -= amount;
        this.totalSpent += amount;
        
        // Create transaction record
        const transaction = {
            id: this.generateTransactionId(),
            type: 'spent',
            amount: amount,
            reason: reason,
            timestamp: Date.now(),
            balanceAfter: this.playerBalance,
            metadata: metadata
        };
        
        // Add to history
        this.addTransaction(transaction);
        
        console.log(`Spent ${amount} WISH tokens for ${reason}. New balance: ${this.playerBalance}`);
        
        // Trigger callbacks
        this.triggerBalanceUpdate();
        this.triggerTransaction(transaction);
        
        return { 
            success: true, 
            transaction: transaction,
            newBalance: this.playerBalance
        };
    }
    
    /**
     * Check if player can afford a purchase
     * @param {number} amount - Amount to check
     * @returns {boolean} Whether player can afford the amount
     */
    canAfford(amount) {
        return this.playerBalance >= amount;
    }
    
    /**
     * Get current token balance
     * @returns {number} Current balance
     */
    getBalance() {
        return this.playerBalance;
    }
    
    /**
     * Get token economy statistics
     * @returns {object} Economy statistics
     */
    getStatistics() {
        const netProfit = this.totalEarned - this.totalSpent;
        const averageEarningPerLevel = this.performanceMetrics.levelsCompleted > 0 
            ? this.totalEarned / this.performanceMetrics.levelsCompleted 
            : 0;
        
        return {
            currentBalance: this.playerBalance,
            totalEarned: this.totalEarned,
            totalSpent: this.totalSpent,
            netProfit: netProfit,
            transactionCount: this.transactionHistory.length,
            averageEarningPerLevel: Math.floor(averageEarningPerLevel),
            performanceMetrics: { ...this.performanceMetrics }
        };
    }
    
    /**
     * Get recent transaction history
     * @param {number} count - Number of recent transactions to return
     * @returns {Array} Recent transactions
     */
    getRecentTransactions(count = 10) {
        return this.transactionHistory
            .slice(-count)
            .reverse(); // Most recent first
    }
    
    /**
     * Add transaction to history
     * @param {object} transaction - Transaction to add
     */
    addTransaction(transaction) {
        this.transactionHistory.push(transaction);
        
        // Limit history size
        if (this.transactionHistory.length > this.maxHistorySize) {
            this.transactionHistory.shift();
        }
    }
    
    /**
     * Generate unique transaction ID
     * @returns {string} Transaction ID
     */
    generateTransactionId() {
        return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Update performance metrics
     * @param {string} reason - Reason for the update
     * @param {object} metadata - Additional metadata
     */
    updatePerformanceMetrics(reason, metadata) {
        if (reason === 'level_completion') {
            this.performanceMetrics.levelsCompleted++;
            
            if (metadata.score) {
                this.performanceMetrics.totalScore += metadata.score;
            }
            
            if (metadata.completionTime) {
                // Update average completion time
                const currentAvg = this.performanceMetrics.averageCompletionTime;
                const count = this.performanceMetrics.levelsCompleted;
                this.performanceMetrics.averageCompletionTime = 
                    (currentAvg * (count - 1) + metadata.completionTime) / count;
            }
            
            if (metadata.bonuses) {
                if (metadata.bonuses.perfect) this.performanceMetrics.perfectCompletions++;
                if (metadata.bonuses.speed) this.performanceMetrics.speedBonuses++;
                if (metadata.bonuses.accuracy) this.performanceMetrics.accuracyBonuses++;
            }
        }
    }
    
    /**
     * Add pending reward for visual feedback
     * @param {number} amount - Reward amount
     * @param {string} reason - Reason for reward
     */
    addPendingReward(amount, reason) {
        const reward = {
            id: this.generateTransactionId(),
            amount: amount,
            reason: reason,
            timestamp: Date.now(),
            displayed: false
        };
        
        this.pendingRewards.push(reward);
    }
    
    /**
     * Get pending rewards for display
     * @returns {Array} Pending rewards
     */
    getPendingRewards() {
        return this.pendingRewards.filter(reward => !reward.displayed);
    }
    
    /**
     * Mark reward as displayed
     * @param {string} rewardId - Reward ID to mark as displayed
     */
    markRewardDisplayed(rewardId) {
        const reward = this.pendingRewards.find(r => r.id === rewardId);
        if (reward) {
            reward.displayed = true;
        }
        
        // Clean up old displayed rewards
        const cutoffTime = Date.now() - 10000; // 10 seconds
        this.pendingRewards = this.pendingRewards.filter(
            reward => !reward.displayed || reward.timestamp > cutoffTime
        );
    }
    
    /**
     * Update reward animations
     * @param {number} deltaTime - Time elapsed since last update
     */
    updateRewardAnimations(deltaTime) {
        // Update existing animations
        for (let i = this.rewardAnimations.length - 1; i >= 0; i--) {
            const animation = this.rewardAnimations[i];
            animation.elapsed += deltaTime;
            
            // Remove completed animations
            if (animation.elapsed >= animation.duration) {
                this.rewardAnimations.splice(i, 1);
            }
        }
        
        // Add new animations for pending rewards
        const pendingRewards = this.getPendingRewards();
        for (const reward of pendingRewards) {
            this.rewardAnimations.push({
                id: reward.id,
                amount: reward.amount,
                reason: reward.reason,
                startTime: Date.now(),
                elapsed: 0,
                duration: 3000, // 3 second animation
                startY: 100,
                endY: 50,
                alpha: 1.0
            });
            
            this.markRewardDisplayed(reward.id);
        }
    }
    
    /**
     * Render token balance and reward animations
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} deltaTime - Time elapsed since last update
     */
    render(ctx, deltaTime) {
        // Update animations
        this.updateRewardAnimations(deltaTime);
        
        // Render token balance (top-right corner)
        this.renderTokenBalance(ctx);
        
        // Render reward animations
        this.renderRewardAnimations(ctx);
    }
    
    /**
     * Render token balance display
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderTokenBalance(ctx) {
        const x = ctx.canvas.width - 20;
        const y = 80;
        
        // Background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(x - 150, y - 25, 140, 30);
        
        // Border
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.strokeRect(x - 150, y - 25, 140, 30);
        
        // Token icon (simple star)
        ctx.fillStyle = '#FFD700';
        ctx.font = '16px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('★', x - 145, y - 5);
        
        // Balance text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '14px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(`${this.playerBalance.toLocaleString()} WISH`, x - 10, y - 5);
    }
    
    /**
     * Render reward animations
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderRewardAnimations(ctx) {
        for (const animation of this.rewardAnimations) {
            const progress = animation.elapsed / animation.duration;
            const y = animation.startY + (animation.endY - animation.startY) * progress;
            const alpha = Math.max(0, 1.0 - progress);
            
            // Reward text
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.fillStyle = '#00FF00';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`+${animation.amount} WISH`, ctx.canvas.width / 2, y);
            
            // Reason text
            ctx.fillStyle = '#FFFFFF';
            ctx.font = '12px Arial';
            ctx.fillText(this.formatRewardReason(animation.reason), ctx.canvas.width / 2, y + 20);
            ctx.restore();
        }
    }
    
    /**
     * Format reward reason for display
     * @param {string} reason - Reward reason
     * @returns {string} Formatted reason
     */
    formatRewardReason(reason) {
        const reasonMap = {
            'level_completion': 'Level Complete',
            'speed_bonus': 'Speed Bonus',
            'accuracy_bonus': 'Accuracy Bonus',
            'perfect_bonus': 'Perfect Run',
            'enemy_defeat': 'Enemy Defeated',
            'wave_completion': 'Wave Complete'
        };
        
        return reasonMap[reason] || reason.replace(/_/g, ' ').toUpperCase();
    }
    
    /**
     * Reset token economy (for new game)
     */
    reset() {
        this.playerBalance = 0;
        this.totalEarned = 0;
        this.totalSpent = 0;
        this.transactionHistory = [];
        this.performanceMetrics = {
            levelsCompleted: 0,
            totalScore: 0,
            averageCompletionTime: 0,
            perfectCompletions: 0,
            speedBonuses: 0,
            accuracyBonuses: 0
        };
        this.pendingRewards = [];
        this.rewardAnimations = [];
        
        console.log('TokenEconomyManager reset');
    }
    
    /**
     * Trigger balance update callback
     */
    triggerBalanceUpdate() {
        if (this.onBalanceUpdateCallback) {
            this.onBalanceUpdateCallback(this.playerBalance, this.getStatistics());
        }
    }
    
    /**
     * Trigger transaction callback
     * @param {object} transaction - Transaction data
     */
    triggerTransaction(transaction) {
        if (this.onTransactionCallback) {
            this.onTransactionCallback(transaction);
        }
    }
    
    /**
     * Trigger reward earned callback
     * @param {number} amount - Reward amount
     * @param {string} reason - Reward reason
     * @param {object} metadata - Additional metadata
     */
    triggerRewardEarned(amount, reason, metadata) {
        if (this.onRewardEarnedCallback) {
            this.onRewardEarnedCallback(amount, reason, metadata);
        }
    }
    
    /**
     * Set callback for balance updates
     * @param {Function} callback - Callback function
     */
    setOnBalanceUpdate(callback) {
        this.onBalanceUpdateCallback = callback;
    }
    
    /**
     * Set callback for transactions
     * @param {Function} callback - Callback function
     */
    setOnTransaction(callback) {
        this.onTransactionCallback = callback;
    }
    
    /**
     * Set callback for reward earned events
     * @param {Function} callback - Callback function
     */
    setOnRewardEarned(callback) {
        this.onRewardEarnedCallback = callback;
    }
}