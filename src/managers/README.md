# Managers Directory

This directory will contain manager classes that handle specific game subsystems:

- `AuthManager.js` - Orange ID authentication and debug bypass
- `TokenEconomyManager.js` - WISH token tracking and transactions
- `RealityWarpManager.js` - Reality warp system and AI integration
- `EnemyManager.js` - Enemy spawning and lifecycle management
- `LevelManager.js` - Level progression and configuration
- `OrangeSDKManager.js` - Progress persistence via Orange SDK
- `RaffleManager.js` - Daily/weekly raffle system
- `GenieInterface.js` - Between-level interface for purchases
- `EnvironmentAnalyzer.js` - LLM analysis for environmental effects
- `AIIntegrationManager.js` - Fal.ai and LLM service integration