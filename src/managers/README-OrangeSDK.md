# Orange SDK Integration

This document describes the Orange SDK integration for WarpSpace Defense, which enables data persistence for tournament play on the Orange Network platform.

## Overview

The Orange SDK integration provides:
- **Data Persistence Only**: Saves player progress and statistics to Orange SDK (no loading/restoration)
- **Special Status Handling**: Checks for special statuses like login streaks that provide gameplay bonuses
- **Automatic Saving**: Tri<PERSON><PERSON> saves on level completion, token changes, and game events
- **Tournament Integration**: Sends required events for tournament platform compatibility

## Architecture

### OrangeSDKManager Class

The `OrangeSDKManager` is the main class that handles all Orange SDK interactions:

```javascript
import { OrangeSDKManager } from './managers/OrangeSDKManager.js';

const sdkManager = new OrangeSDKManager();
await sdkManager.initialize();
```

### Data Structure

The manager tracks comprehensive player data:

```javascript
{
  // Core progress
  totalScore: 0,
  highestLevel: 0,
  wishTokensEarned: 0,
  wishTokensSpent: 0,
  wishTokensBalance: 0,
  
  // Level completion tracking
  levelsCompleted: [],
  levelBestScores: {},
  totalPlayTime: 0,
  
  // Performance metrics
  perfectCompletions: 0,
  totalEnemiesDefeated: 0,
  averageCompletionTime: 0,
  
  // Special statuses (affects gameplay)
  specialStatuses: {
    loginStreak: 0,
    lastLoginDate: null,
    bonusLivesEarned: 0,
    tournamentParticipant: false,
    achievementUnlocks: []
  }
}
```

## Integration Points

### 1. Game Engine Integration

The `GameEngine` automatically integrates with Orange SDK:

```javascript
// In GameEngine.initializeSystems()
this.orangeSDKManager = new OrangeSDKManager();
this.setupOrangeSDKCallbacks();
this.initializeOrangeSDK();
```

### 2. Level Completion

Automatically saves progress when levels are completed:

```javascript
// Triggered by LevelManager
this.orangeSDKManager.onLevelCompleted(completionData);
```

### 3. Token Changes

Monitors token economy changes and saves significant updates:

```javascript
// Triggered by TokenEconomyManager
this.orangeSDKManager.onTokensChanged({
  earned: 100,
  spent: 50
});
```

### 4. Game Events

Handles standard Orange SDK game events:

```javascript
// Game lifecycle events
GGSDK.gameLoaded();
GGSDK.gameOver(score);
GGSDK.gamePaused();
GGSDK.gameResumed();
```

## Special Status System

### Login Streak Bonuses

The system tracks consecutive daily logins and awards bonus lives:

- **2-7 day streak**: Awards 1-3 bonus lives at game start
- **Streak tracking**: Automatically maintained across sessions
- **Bonus application**: Applied when game initializes

```javascript
// Example: 3-day login streak = 2 bonus lives
if (loginStreak >= 2) {
  const bonusLives = Math.min(loginStreak - 1, 3);
  playerShip.addLives(bonusLives);
}
```

### Tournament Participant Status

Special status for tournament participants:

```javascript
sdkManager.setTournamentParticipant(true);
// Enables tournament-specific features
```

### Achievement System

Track and unlock achievements:

```javascript
sdkManager.unlockAchievement('first_level_complete');
sdkManager.unlockAchievement('perfect_completion_streak');
```

## Auto-Save System

### Automatic Triggers

The system automatically saves data on:

1. **Level Completion**: After each level is completed
2. **Significant Token Changes**: When earning/spending 50+ tokens
3. **Game Pause**: When game is paused by user or system
4. **Game Quit**: Final save before game ends
5. **Periodic Auto-Save**: Every 30 seconds during gameplay

### Manual Saving

Force a save operation:

```javascript
await sdkManager.savePlayerData('manual_save');
```

### Save Configuration

Configure auto-save behavior:

```javascript
sdkManager.setAutoSave(true, 30000); // Enable with 30s interval
sdkManager.setAutoSave(false);       // Disable auto-save
```

## Error Handling

### Retry Logic

The system includes robust error handling:

- **Automatic Retries**: Up to 3 attempts with exponential backoff
- **Graceful Degradation**: Game continues if saves fail
- **Error Callbacks**: Notifications for save failures

```javascript
sdkManager.setOnSaveErrorCallback((error, reason) => {
  console.error(`Save failed (${reason}):`, error);
  // Handle error (show notification, retry later, etc.)
});
```

### Conflict Resolution

Handles concurrent save operations:

- **Save Queuing**: Queues saves if one is in progress
- **Data Merging**: Ensures no data loss during rapid updates
- **State Validation**: Verifies data integrity before saving

## Testing

### Test Page

Use `test-orange-sdk.html` to test integration:

1. Open the test page in a browser
2. Click "Initialize SDK" to start
3. Test various operations (save, load, events)
4. Verify data persistence and special statuses

### Manual Testing

```javascript
// Test level completion
await sdkManager.onLevelCompleted({
  levelNumber: 1,
  completed: true,
  score: { totalScore: 1500, tokenReward: 100 },
  completionTime: 45.2
});

// Test token changes
await sdkManager.onTokensChanged({ earned: 75 });

// Test special status
sdkManager.unlockAchievement('test_achievement');
```

## Performance Considerations

### Efficient Saving

- **Batched Updates**: Groups related changes into single saves
- **Throttling**: Prevents excessive save operations
- **Compression**: Minimizes data size for network efficiency

### Memory Management

- **Circular Buffer**: Limits transaction history size
- **Data Cleanup**: Removes outdated session data
- **Lazy Loading**: Only loads data when needed

## Troubleshooting

### Common Issues

1. **SDK Not Ready**: Ensure `initialize()` is called and completed
2. **Save Failures**: Check network connectivity and retry logic
3. **Missing Data**: Verify data structure matches expected format
4. **Special Status Not Applied**: Check callback setup and timing

### Debug Information

```javascript
// Get detailed status
const stats = sdkManager.getSaveStatistics();
const playerData = sdkManager.getPlayerData();
const specialStatuses = sdkManager.getSpecialStatuses();

console.log('SDK Stats:', stats);
console.log('Player Data:', playerData);
console.log('Special Statuses:', specialStatuses);
```

## Future Enhancements

Potential improvements for the Orange SDK integration:

1. **Advanced Analytics**: More detailed gameplay metrics
2. **Social Features**: Friend comparisons and leaderboards
3. **Cloud Sync**: Cross-device progress synchronization
4. **Offline Support**: Local storage fallback when SDK unavailable
5. **Real-time Updates**: Live tournament status updates
