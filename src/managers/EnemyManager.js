import { Enemy } from '../entities/Enemy.js';
import { Vector2 } from '../utils/Vector2.js';
import { GameMath } from '../utils/GameMath.js';
import { ENEMY_TYPES, GAME_CONFIG } from '../config/gameConfig.js';
// import { EnemyProjectileSystem } from '../systems/EnemyProjectileSystem.js'; // Disabled - enemies don't fire projectiles

/**
 * EnemyManager handles enemy spawning, wave management, and lifecycle
 * Manages collision detection between enemies and player/projectiles
 */
export class EnemyManager {
    constructor(canvasWidth = GAME_CONFIG.CANVAS_WIDTH, canvasHeight = GAME_CONFIG.CANVAS_HEIGHT, gameObjectManager = null) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        this.gameObjectManager = gameObjectManager;

        // Enemy tracking
        this.activeEnemies = [];
        this.enemyPool = []; // Object pool for performance
        this.maxEnemies = 50;

        // Wave management
        this.currentWave = 0;
        this.waveInProgress = false;
        this.waveStartTime = 0;
        this.waveConfig = null;
        this.enemiesSpawnedInWave = 0;
        this.enemiesKilledInWave = 0;

        // Spawn timing
        this.lastSpawnTime = 0;
        this.spawnCooldown = 1000; // Base spawn cooldown in milliseconds
        this.spawnTimer = 0;

        // Formation management
        this.formations = [];
        this.formationSpawnQueue = [];

        // Environmental effects
        this.currentEnvironment = 'space';
        this.environmentalEffects = this.getDefaultEnvironmentalEffects();

        // Statistics
        this.totalEnemiesSpawned = 0;
        this.totalEnemiesKilled = 0;
        this.totalScore = 0;

        // Collision detection optimization
        this.collisionGrid = null;
        this.gridSize = 64;
        this.gridWidth = Math.ceil(canvasWidth / this.gridSize);
        this.gridHeight = Math.ceil(canvasHeight / this.gridSize);

        // Enemy projectile system disabled - enemies don't fire projectiles in this game
        // this.projectileSystem = new EnemyProjectileSystem(canvasWidth, canvasHeight, gameObjectManager);

        console.log('EnemyManager initialized');
    }

    /**
     * Update all enemies and wave management
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    update(deltaTime, playerPosition = null) {
        // Update spawn timer
        this.spawnTimer += deltaTime;

        // Update wave management
        this.updateWaveManagement(deltaTime);

        // Spawn enemies based on current wave
        this.updateEnemySpawning(deltaTime, playerPosition);

        // Update all active enemies
        this.updateActiveEnemies(deltaTime, playerPosition);

        // Clean up destroyed enemies
        this.cleanupDestroyedEnemies();

        // Update formations
        this.updateFormations(deltaTime);

        // Enemy projectile system disabled - enemies don't fire projectiles
        // this.projectileSystem.update(deltaTime, playerPosition);

        // Enemy attacks disabled - enemies don't fire projectiles
        // this.updateEnemyAttacks(deltaTime, playerPosition);

        // Check wave completion
        this.checkWaveCompletion();
    }

    /**
     * Update wave management logic
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    updateWaveManagement(deltaTime) {
        if (!this.waveInProgress && this.activeEnemies.length === 0) {
            // Start next wave if no enemies are active
            this.startNextWave();
        }

        if (this.waveInProgress) {
            this.waveStartTime += deltaTime;
        }
    }

    /**
     * Update enemy spawning based on current wave configuration
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    updateEnemySpawning(deltaTime, playerPosition) {
        if (!this.waveInProgress || !this.waveConfig) return;

        // Check if we should spawn more enemies
        if (this.spawnTimer >= this.spawnCooldown &&
            this.activeEnemies.length < this.maxEnemies &&
            this.enemiesSpawnedInWave < this.waveConfig.totalEnemies) {

            this.spawnEnemyFromWave();
            this.spawnTimer = 0;
        }

        // Process formation spawn queue
        this.processFormationSpawnQueue(deltaTime);
    }

    /**
     * Update all active enemies
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    updateActiveEnemies(deltaTime, playerPosition) {
        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];

            if (enemy.active) {
                enemy.update(deltaTime, playerPosition);

                // Apply environmental effects
                this.applyEnvironmentalEffects(enemy);
            }
        }
    }

    /**
     * Clean up destroyed or inactive enemies
     */
    cleanupDestroyedEnemies() {
        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];

            if (enemy.destroyed || !enemy.active) {
                // Return enemy to pool
                this.returnEnemyToPool(enemy);
                this.activeEnemies.splice(i, 1);

                if (enemy.isDestroyed) {
                    this.enemiesKilledInWave++;
                    this.totalEnemiesKilled++;
                    this.totalScore += enemy.scoreValue;
                }
            }
        }
    }

    /**
     * Start the next wave
     */
    startNextWave() {
        this.currentWave++;
        this.waveInProgress = true;
        this.waveStartTime = 0;
        this.enemiesSpawnedInWave = 0;
        this.enemiesKilledInWave = 0;

        // Generate wave configuration
        this.waveConfig = this.generateWaveConfig(this.currentWave);

        // Set spawn cooldown based on wave difficulty
        this.spawnCooldown = Math.max(200, 1500 - (this.currentWave * 50));

        console.log(`Starting wave ${this.currentWave}:`, this.waveConfig);
    }

    /**
     * Generate wave configuration based on wave number
     * @param {number} waveNumber - Current wave number
     * @returns {object} Wave configuration
     */
    generateWaveConfig(waveNumber) {
        const baseEnemyCount = 5;
        const enemyCountIncrease = Math.floor(waveNumber / 2);
        const totalEnemies = baseEnemyCount + enemyCountIncrease;

        // Determine enemy type distribution based on wave
        const enemyTypes = this.getWaveEnemyTypes(waveNumber);

        // Create spawn patterns
        const spawnPatterns = this.generateSpawnPatterns(waveNumber, totalEnemies);

        return {
            waveNumber: waveNumber,
            totalEnemies: totalEnemies,
            enemyTypes: enemyTypes,
            spawnPatterns: spawnPatterns,
            difficulty: Math.min(10, Math.floor(waveNumber / 3) + 1),
            hasFormation: waveNumber % 4 === 0, // Every 4th wave has formation
            hasBoss: waveNumber % 10 === 0 // Every 10th wave has boss
        };
    }

    /**
     * Get enemy types for a specific wave
     * @param {number} waveNumber - Wave number
     * @returns {Array} Array of enemy types with weights
     */
    getWaveEnemyTypes(waveNumber) {
        const types = [];

        // Early waves - mostly air enemies
        if (waveNumber <= 3) {
            types.push({ type: ENEMY_TYPES.AIR, weight: 0.8 });
            types.push({ type: ENEMY_TYPES.WATER, weight: 0.2 });
        }
        // Mid waves - introduce more variety
        else if (waveNumber <= 7) {
            types.push({ type: ENEMY_TYPES.AIR, weight: 0.4 });
            types.push({ type: ENEMY_TYPES.WATER, weight: 0.3 });
            types.push({ type: ENEMY_TYPES.FIRE, weight: 0.2 });
            types.push({ type: ENEMY_TYPES.EARTH, weight: 0.1 });
        }
        // Later waves - all types
        else {
            types.push({ type: ENEMY_TYPES.AIR, weight: 0.2 });
            types.push({ type: ENEMY_TYPES.WATER, weight: 0.2 });
            types.push({ type: ENEMY_TYPES.FIRE, weight: 0.2 });
            types.push({ type: ENEMY_TYPES.EARTH, weight: 0.15 });
            types.push({ type: ENEMY_TYPES.CRYSTAL, weight: 0.15 });
            types.push({ type: ENEMY_TYPES.SHADOW, weight: 0.1 });
        }

        return types;
    }

    /**
     * Generate spawn patterns for the wave
     * @param {number} waveNumber - Wave number
     * @param {number} totalEnemies - Total enemies in wave
     * @returns {Array} Array of spawn patterns
     */
    generateSpawnPatterns(waveNumber, totalEnemies) {
        const patterns = [];

        // Simple linear spawning for early waves
        if (waveNumber <= 2) {
            patterns.push({
                type: 'linear',
                count: totalEnemies,
                spacing: 1000,
                movementPattern: 'linear'
            });
        }
        // Add formation patterns
        else if (waveNumber <= 5) {
            patterns.push({
                type: 'formation',
                count: Math.floor(totalEnemies * 0.6),
                formation: 'line',
                movementPattern: 'sine'
            });
            patterns.push({
                type: 'scattered',
                count: Math.ceil(totalEnemies * 0.4),
                movementPattern: 'zigzag'
            });
        }
        // Complex patterns for later waves
        else {
            patterns.push({
                type: 'formation',
                count: Math.floor(totalEnemies * 0.4),
                formation: 'v-formation',
                movementPattern: 'formation'
            });
            patterns.push({
                type: 'dive',
                count: Math.floor(totalEnemies * 0.3),
                movementPattern: 'dive'
            });
            patterns.push({
                type: 'scattered',
                count: Math.ceil(totalEnemies * 0.3),
                movementPattern: 'spiral'
            });
        }

        return patterns;
    }

    /**
     * Spawn an enemy from the current wave configuration
     */
    spawnEnemyFromWave() {
        if (!this.waveConfig || this.enemiesSpawnedInWave >= this.waveConfig.totalEnemies) {
            return;
        }

        // Select enemy type based on wave configuration
        const enemyType = this.selectEnemyType(this.waveConfig.enemyTypes);

        // Select spawn pattern
        const pattern = this.selectSpawnPattern(this.waveConfig.spawnPatterns);

        // Create spawn position
        const spawnPos = this.generateSpawnPosition(pattern);

        // Spawn the enemy
        const enemy = this.spawnEnemy(spawnPos.x, spawnPos.y, enemyType);

        if (enemy) {
            // Apply movement pattern
            enemy.setMovementPattern(pattern.movementPattern, {
                amplitude: GameMath.random(30, 80),
                frequency: GameMath.random(1, 3)
            });

            // Handle formation spawning
            if (pattern.type === 'formation') {
                this.addEnemyToFormation(enemy, pattern);
            }

            this.enemiesSpawnedInWave++;
        }
    }

    /**
     * Select enemy type based on weighted distribution
     * @param {Array} enemyTypes - Array of enemy types with weights
     * @returns {string} Selected enemy type
     */
    selectEnemyType(enemyTypes) {
        const random = Math.random();
        let cumulativeWeight = 0;

        for (const typeData of enemyTypes) {
            cumulativeWeight += typeData.weight;
            if (random <= cumulativeWeight) {
                return typeData.type;
            }
        }

        // Fallback to first type
        return enemyTypes[0].type;
    }

    /**
     * Select spawn pattern from available patterns
     * @param {Array} patterns - Available spawn patterns
     * @returns {object} Selected spawn pattern
     */
    selectSpawnPattern(patterns) {
        return patterns[Math.floor(Math.random() * patterns.length)];
    }

    /**
     * Generate spawn position based on pattern
     * @param {object} pattern - Spawn pattern configuration
     * @returns {Vector2} Spawn position
     */
    generateSpawnPosition(pattern) {
        switch (pattern.type) {
            case 'linear':
                return new Vector2(
                    GameMath.random(50, this.canvasWidth - 50),
                    -30
                );

            case 'formation':
                return new Vector2(
                    this.canvasWidth / 2,
                    -50
                );

            case 'scattered':
                return new Vector2(
                    GameMath.random(30, this.canvasWidth - 30),
                    GameMath.random(-50, -20)
                );

            case 'dive':
                // Spawn from sides for dive attacks
                const side = Math.random() < 0.5 ? 'left' : 'right';
                return new Vector2(
                    side === 'left' ? -30 : this.canvasWidth + 30,
                    GameMath.random(50, 150)
                );

            default:
                return new Vector2(
                    GameMath.random(50, this.canvasWidth - 50),
                    -30
                );
        }
    }

    /**
     * Spawn a new enemy at the specified position
     * @param {number} x - X position
     * @param {number} y - Y position
     * @param {string} type - Enemy type
     * @returns {Enemy|null} Spawned enemy or null if failed
     */
    spawnEnemy(x, y, type = ENEMY_TYPES.AIR) {
        // Try to get enemy from pool first
        let enemy = this.getEnemyFromPool();

        if (!enemy) {
            // Create new enemy if pool is empty
            enemy = new Enemy(x, y, type, this.canvasWidth, this.canvasHeight);
        } else {
            // Reset pooled enemy
            enemy.reset();
            enemy.position.set(x, y);
            enemy.type = type;
            enemy.maxHealth = enemy.getTypeMaxHealth(type);
            enemy.health = enemy.maxHealth;
            enemy.baseSpeed = enemy.getTypeBaseSpeed(type);
            enemy.currentSpeed = enemy.baseSpeed;
            enemy.scoreValue = enemy.getTypeScoreValue(type);
        }

        // Apply environmental effects
        this.applyEnvironmentalEffects(enemy);

        // Add to active enemies
        this.activeEnemies.push(enemy);
        this.totalEnemiesSpawned++;

        // Register with game object manager if available
        if (this.gameObjectManager) {
            this.gameObjectManager.add(enemy);
        }

        console.log(`Spawned ${type} enemy at (${x}, ${y}). Active enemies: ${this.activeEnemies.length}`);
        return enemy;
    }

    /**
     * Get enemy from object pool
     * @returns {Enemy|null} Pooled enemy or null if pool is empty
     */
    getEnemyFromPool() {
        return this.enemyPool.length > 0 ? this.enemyPool.pop() : null;
    }

    /**
     * Return enemy to object pool
     * @param {Enemy} enemy - Enemy to return to pool
     */
    returnEnemyToPool(enemy) {
        if (this.enemyPool.length < this.maxEnemies) {
            enemy.reset();
            this.enemyPool.push(enemy);
        }

        // Remove from game object manager if available
        if (this.gameObjectManager) {
            this.gameObjectManager.remove(enemy);
        }
    }

    /**
     * Add enemy to formation
     * @param {Enemy} enemy - Enemy to add to formation
     * @param {object} pattern - Formation pattern
     */
    addEnemyToFormation(enemy, pattern) {
        const formation = this.getOrCreateFormation(pattern.formation);

        // Calculate formation position
        const formationIndex = formation.enemies.length;
        const offset = this.calculateFormationOffset(pattern.formation, formationIndex);

        enemy.setFormationTarget(formation.center, offset);
        formation.enemies.push(enemy);

        console.log(`Added enemy to ${pattern.formation} formation. Formation size: ${formation.enemies.length}`);
    }

    /**
     * Get or create formation
     * @param {string} formationType - Type of formation
     * @returns {object} Formation object
     */
    getOrCreateFormation(formationType) {
        let formation = this.formations.find(f => f.type === formationType && f.enemies.length < 8);

        if (!formation) {
            formation = {
                type: formationType,
                center: new Vector2(this.canvasWidth / 2, 100),
                enemies: [],
                movementTimer: 0
            };
            this.formations.push(formation);
        }

        return formation;
    }

    /**
     * Calculate formation offset for enemy position
     * @param {string} formationType - Type of formation
     * @param {number} index - Enemy index in formation
     * @returns {Vector2} Formation offset
     */
    calculateFormationOffset(formationType, index) {
        switch (formationType) {
            case 'line':
                return new Vector2((index - 2) * 40, 0);

            case 'v-formation':
                const side = index % 2 === 0 ? -1 : 1;
                const row = Math.floor(index / 2);
                return new Vector2(side * (row + 1) * 30, row * 25);

            case 'diamond':
                const positions = [
                    new Vector2(0, -30),    // Top
                    new Vector2(-30, 0),    // Left
                    new Vector2(30, 0),     // Right
                    new Vector2(0, 30),     // Bottom
                    new Vector2(-20, -15),  // Top-left
                    new Vector2(20, -15),   // Top-right
                    new Vector2(-20, 15),   // Bottom-left
                    new Vector2(20, 15)     // Bottom-right
                ];
                return positions[index % positions.length];

            default:
                return new Vector2((index - 2) * 35, 0);
        }
    }

    /**
     * Update formations
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    updateFormations(deltaTime) {
        for (let i = this.formations.length - 1; i >= 0; i--) {
            const formation = this.formations[i];
            formation.movementTimer += deltaTime / 1000;

            // Remove destroyed enemies from formation
            formation.enemies = formation.enemies.filter(enemy => enemy.active && !enemy.destroyed);

            // Remove empty formations
            if (formation.enemies.length === 0) {
                this.formations.splice(i, 1);
                continue;
            }

            // Update formation center position (slow movement)
            formation.center.y += 20 * (deltaTime / 1000);

            // Add some horizontal movement
            formation.center.x += Math.sin(formation.movementTimer * 0.5) * 10 * (deltaTime / 1000);

            // Keep formation on screen
            formation.center.x = GameMath.clamp(formation.center.x, 100, this.canvasWidth - 100);
        }
    }

    /**
     * Process formation spawn queue
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    processFormationSpawnQueue(deltaTime) {
        // Implementation for queued formation spawning
        // This can be used for more complex formation spawning patterns
    }

    /**
     * Check if current wave is complete
     */
    checkWaveCompletion() {
        if (this.waveInProgress &&
            this.enemiesSpawnedInWave >= this.waveConfig.totalEnemies &&
            this.activeEnemies.length === 0) {

            this.completeWave();
        }
    }

    /**
     * Complete the current wave
     */
    completeWave() {
        this.waveInProgress = false;

        console.log(`Wave ${this.currentWave} completed! Enemies killed: ${this.enemiesKilledInWave}/${this.waveConfig.totalEnemies}`);

        // Calculate wave completion bonus
        const completionBonus = this.calculateWaveBonus();
        this.totalScore += completionBonus;

        // Clear formations
        this.formations = [];

        // Trigger wave completion event
        this.onWaveComplete(this.currentWave, completionBonus);
    }

    /**
     * Calculate wave completion bonus
     * @returns {number} Bonus score
     */
    calculateWaveBonus() {
        const baseBonus = 100;
        const waveMultiplier = this.currentWave;
        const completionRatio = this.enemiesKilledInWave / this.waveConfig.totalEnemies;

        return Math.floor(baseBonus * waveMultiplier * completionRatio);
    }

    /**
     * Wave completion callback (override in game)
     * @param {number} waveNumber - Completed wave number
     * @param {number} bonus - Completion bonus
     */
    onWaveComplete(waveNumber, bonus) {
        // Override this method in the game to handle wave completion
        console.log(`Wave ${waveNumber} complete with bonus: ${bonus}`);
    }

    /**
     * Render all active enemies and projectiles
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} interpolation - Interpolation factor for smooth rendering
     */
    render(ctx, interpolation = 0) {
        // Render enemies
        for (const enemy of this.activeEnemies) {
            if (enemy.visible) {
                enemy.render(ctx, interpolation);
            }
        }

        // Enemy projectiles disabled - enemies don't fire projectiles
        // this.projectileSystem.render(ctx, interpolation);

        // Render debug information
        if (window.DEBUG_MODE) {
            this.renderDebugInfo(ctx);
        }
    }

    /**
     * Render debug information
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderDebugInfo(ctx) {
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';

        const debugInfo = [
            `Wave: ${this.currentWave}`,
            `Active Enemies: ${this.activeEnemies.length}`,
            `Spawned: ${this.enemiesSpawnedInWave}/${this.waveConfig?.totalEnemies || 0}`,
            `Killed: ${this.enemiesKilledInWave}`,
            `Total Score: ${this.totalScore}`,
            `Formations: ${this.formations.length}`
        ];

        for (let i = 0; i < debugInfo.length; i++) {
            ctx.fillText(debugInfo[i], 10, 20 + i * 15);
        }

        // Draw formation centers
        ctx.strokeStyle = '#FFFF00';
        ctx.lineWidth = 2;
        for (const formation of this.formations) {
            ctx.beginPath();
            ctx.arc(formation.center.x, formation.center.y, 5, 0, Math.PI * 2);
            ctx.stroke();
        }
    }

    /**
     * Check collision between player and enemies
     * @param {PlayerShip} player - Player ship
     * @returns {Array} Array of colliding enemies
     */
    checkPlayerCollisions(player) {
        const collisions = [];

        if (!player.active || player.isInvulnerable) {
            return collisions;
        }

        for (const enemy of this.activeEnemies) {
            if (enemy.active && !enemy.isDestroyed && enemy.collidesWith(player)) {
                collisions.push(enemy);
            }
        }

        return collisions;
    }

    /**
     * Check collision between projectiles and enemies
     * @param {Array} projectiles - Array of projectiles
     * @returns {Array} Array of collision results
     */
    checkProjectileCollisions(projectiles) {
        const collisions = [];

        for (const projectile of projectiles) {
            if (!projectile.active || projectile.hasTag('enemy')) {
                continue; // Skip enemy projectiles
            }

            for (const enemy of this.activeEnemies) {
                if (enemy.active && !enemy.isDestroyed && enemy.collidesWith(projectile)) {
                    collisions.push({
                        projectile: projectile,
                        enemy: enemy,
                        damage: projectile.damage || 25
                    });
                    break; // Projectile can only hit one enemy
                }
            }
        }

        return collisions;
    }

    /**
     * Handle collision between player and enemy
     * @param {PlayerShip} player - Player ship
     * @param {Enemy} enemy - Colliding enemy
     */
    handlePlayerEnemyCollision(player, enemy) {
        // Player takes damage
        const damageResult = player.takeDamage(enemy.getAttackDamage());

        // Enemy is destroyed on collision
        enemy.takeDamage(enemy.health);

        console.log(`Player-Enemy collision: Player took ${damageResult.damageTaken} damage, Enemy destroyed`);

        return {
            playerDamage: damageResult.damageTaken,
            enemyDestroyed: true,
            scoreGained: enemy.scoreValue
        };
    }

    /**
     * Handle collision between projectile and enemy
     * @param {Projectile} projectile - Projectile
     * @param {Enemy} enemy - Enemy
     * @param {number} damage - Damage amount
     */
    handleProjectileEnemyCollision(projectile, enemy, damage) {
        // Enemy takes damage
        const damageResult = enemy.takeDamage(damage);

        // Destroy projectile
        projectile.destroy();

        console.log(`Projectile-Enemy collision: Enemy took ${damageResult.damageTaken} damage`);

        return {
            enemyDestroyed: damageResult.destroyed,
            scoreGained: damageResult.scoreValue,
            damageDealt: damageResult.damageTaken
        };
    }

    /**
     * Apply environmental effects to enemy
     * @param {Enemy} enemy - Enemy to apply effects to
     */
    applyEnvironmentalEffects(enemy) {
        const effectiveness = this.calculateEnvironmentalEffectiveness(enemy.type, this.currentEnvironment);
        enemy.applyEnvironmentalEffect(this.currentEnvironment, effectiveness);
    }

    /**
     * Calculate environmental effectiveness for enemy type
     * @param {string} enemyType - Enemy type
     * @param {string} environment - Current environment
     * @returns {number} Effectiveness multiplier
     */
    calculateEnvironmentalEffectiveness(enemyType, environment) {
        const effectivenessMap = this.environmentalEffects[environment];
        return effectivenessMap ? (effectivenessMap[enemyType] || 1.0) : 1.0;
    }

    /**
     * Set current environment and update all enemies
     * @param {string} environment - New environment type
     * @param {object} effects - Environmental effects configuration
     */
    setEnvironment(environment, effects = null) {
        this.currentEnvironment = environment;

        if (effects) {
            this.environmentalEffects[environment] = effects;
        }

        // Update all active enemies
        for (const enemy of this.activeEnemies) {
            this.applyEnvironmentalEffects(enemy);
        }

        console.log(`Environment changed to: ${environment}`);
    }

    /**
     * Get default environmental effects
     * @returns {object} Default environmental effects configuration
     */
    getDefaultEnvironmentalEffects() {
        return {
            space: {
                [ENEMY_TYPES.AIR]: 1.2,
                [ENEMY_TYPES.WATER]: 0.8,
                [ENEMY_TYPES.FIRE]: 1.0,
                [ENEMY_TYPES.EARTH]: 0.9,
                [ENEMY_TYPES.CRYSTAL]: 1.1,
                [ENEMY_TYPES.SHADOW]: 1.0
            },
            underwater: {
                [ENEMY_TYPES.AIR]: 0.6,
                [ENEMY_TYPES.WATER]: 1.5,
                [ENEMY_TYPES.FIRE]: 0.3,
                [ENEMY_TYPES.EARTH]: 0.8,
                [ENEMY_TYPES.CRYSTAL]: 1.0,
                [ENEMY_TYPES.SHADOW]: 0.9
            },
            volcanic: {
                [ENEMY_TYPES.AIR]: 0.8,
                [ENEMY_TYPES.WATER]: 0.4,
                [ENEMY_TYPES.FIRE]: 1.6,
                [ENEMY_TYPES.EARTH]: 1.3,
                [ENEMY_TYPES.CRYSTAL]: 0.9,
                [ENEMY_TYPES.SHADOW]: 0.7
            },
            crystal: {
                [ENEMY_TYPES.AIR]: 1.0,
                [ENEMY_TYPES.WATER]: 0.9,
                [ENEMY_TYPES.FIRE]: 0.8,
                [ENEMY_TYPES.EARTH]: 1.1,
                [ENEMY_TYPES.CRYSTAL]: 1.8,
                [ENEMY_TYPES.SHADOW]: 1.2
            },
            forest: {
                [ENEMY_TYPES.AIR]: 0.7,
                [ENEMY_TYPES.WATER]: 1.1,
                [ENEMY_TYPES.FIRE]: 0.6,
                [ENEMY_TYPES.EARTH]: 1.4,
                [ENEMY_TYPES.CRYSTAL]: 0.8,
                [ENEMY_TYPES.SHADOW]: 1.3
            }
        };
    }

    /**
     * Update enemy attacks and projectile firing
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's position
     */
    updateEnemyAttacks(deltaTime, playerPosition) {
        if (!playerPosition) return;

        for (const enemy of this.activeEnemies) {
            if (enemy.active && !enemy.isDestroyed && enemy.canAttackPlayer(playerPosition)) {
                const attackData = enemy.attack(playerPosition);
                if (attackData) {
                    // Enemy projectiles disabled - enemies don't fire projectiles
                    // this.projectileSystem.fireProjectile(
                    //     attackData.position,
                    //     attackData.direction,
                    //     attackData.type,
                    //     { damage: attackData.damage }
                    // );
                }
            }
        }
    }

    /**
     * Get enemies that can attack the player
     * @param {Vector2} playerPosition - Player's position
     * @returns {Array} Array of enemies that can attack
     */
    getEnemiesInAttackRange(playerPosition) {
        return this.activeEnemies.filter(enemy =>
            enemy.canAttackPlayer(playerPosition)
        );
    }

    /**
     * Check collision between enemy projectiles and player
     * @param {PlayerShip} player - Player ship
     * @returns {Array} Array of colliding enemy projectiles
     */
    checkEnemyProjectileCollisions(player) {
        // Enemy projectiles disabled - enemies don't fire projectiles
        return [];
        // return this.projectileSystem.checkPlayerCollisions(player);
    }

    /**
     * Handle collision between enemy projectile and player
     * @param {EnemyProjectile} projectile - Enemy projectile
     * @param {PlayerShip} player - Player ship
     * @returns {object} Collision result
     */
    handleEnemyProjectileCollision(projectile, player) {
        // Enemy projectiles disabled - enemies don't fire projectiles
        return { damage: 0, destroyed: false };
        // return this.projectileSystem.handlePlayerCollision(projectile, player);
    }

    /**
     * Trigger enemy attacks
     * @param {Vector2} playerPosition - Player's position
     * @returns {Array} Array of attack data from enemies
     */
    triggerEnemyAttacks(playerPosition) {
        const attacks = [];
        const attackingEnemies = this.getEnemiesInAttackRange(playerPosition);

        for (const enemy of attackingEnemies) {
            const attackData = enemy.attack(playerPosition);
            if (attackData) {
                attacks.push(attackData);
            }
        }

        return attacks;
    }

    /**
     * Get current wave status
     * @returns {object} Wave status information
     */
    getWaveStatus() {
        return {
            currentWave: this.currentWave,
            waveInProgress: this.waveInProgress,
            enemiesSpawned: this.enemiesSpawnedInWave,
            enemiesKilled: this.enemiesKilledInWave,
            totalEnemies: this.waveConfig?.totalEnemies || 0,
            activeEnemies: this.activeEnemies.length,
            totalScore: this.totalScore,
            waveProgress: this.waveConfig ? (this.enemiesSpawnedInWave / this.waveConfig.totalEnemies) : 0
        };
    }

    /**
     * Reset enemy manager (for new game)
     */
    reset() {
        // Clear all enemies
        for (const enemy of this.activeEnemies) {
            enemy.destroy();
        }
        this.activeEnemies = [];

        // Reset wave management
        this.currentWave = 0;
        this.waveInProgress = false;
        this.waveConfig = null;
        this.enemiesSpawnedInWave = 0;
        this.enemiesKilledInWave = 0;

        // Reset timers
        this.spawnTimer = 0;
        this.waveStartTime = 0;

        // Clear formations
        this.formations = [];
        this.formationSpawnQueue = [];

        // Reset statistics
        this.totalEnemiesSpawned = 0;
        this.totalEnemiesKilled = 0;
        this.totalScore = 0;

        // Reset environment
        this.currentEnvironment = 'space';

        // Reset projectile system disabled - enemies don't fire projectiles
        // this.projectileSystem.reset();

        console.log('EnemyManager reset');
    }

    /**
     * Update canvas dimensions (for window resize)
     * @param {number} width - New canvas width
     * @param {number} height - New canvas height
     */
    updateCanvasDimensions(width, height) {
        this.canvasWidth = width;
        this.canvasHeight = height;

        // Update grid dimensions
        this.gridWidth = Math.ceil(width / this.gridSize);
        this.gridHeight = Math.ceil(height / this.gridSize);

        // Update projectile system disabled - enemies don't fire projectiles
        // this.projectileSystem.updateCanvasDimensions(width, height);

        // Update all active enemies
        for (const enemy of this.activeEnemies) {
            enemy.canvasWidth = width;
            enemy.canvasHeight = height;
        }
    }

    /**
     * Get statistics for display
     * @returns {object} Statistics object
     */
    getStatistics() {
        return {
            currentWave: this.currentWave,
            totalEnemiesSpawned: this.totalEnemiesSpawned,
            totalEnemiesKilled: this.totalEnemiesKilled,
            totalScore: this.totalScore,
            activeEnemies: this.activeEnemies.length,
            formations: this.formations.length,
            killRatio: this.totalEnemiesSpawned > 0 ? (this.totalEnemiesKilled / this.totalEnemiesSpawned) : 0
        };
    }
}