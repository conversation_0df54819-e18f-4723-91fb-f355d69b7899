import { Vector2 } from '../utils/Vector2.js';

/**
 * InputManager - <PERSON><PERSON> keyboard, mouse, and touch input
 * Provides unified input interface for ship controls
 */
export class InputManager {
    constructor(canvas) {
        this.canvas = canvas;
        
        // Input state tracking
        this.keys = new Map();
        this.keysPressed = new Map();
        this.keysReleased = new Map();
        
        this.mousePosition = new Vector2(0, 0);
        this.mouseButtons = new Map();
        this.mousePressed = new Map();
        this.mouseReleased = new Map();
        
        this.touches = new Map();
        this.touchStarted = new Map();
        this.touchEnded = new Map();
        
        // Input mapping configuration
        this.keyMappings = new Map();
        this.setupDefaultMappings();
        
        // Mobile/touch support
        this.isTouchDevice = 'ontouchstart' in window;
        this.virtualJoystick = null;
        
        // Bind event handlers
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleKeyUp = this.handleKeyUp.bind(this);
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleTouchStart = this.handleTouchStart.bind(this);
        this.handleTouchEnd = this.handleTouchEnd.bind(this);
        this.handleTouchMove = this.handleTouchMove.bind(this);
        
        // Initialize event listeners
        this.init();
    }
    
    init() {
        // Keyboard events
        document.addEventListener('keydown', this.handleKeyDown);
        document.addEventListener('keyup', this.handleKeyUp);
        
        // Mouse events
        this.canvas.addEventListener('mousedown', this.handleMouseDown);
        this.canvas.addEventListener('mouseup', this.handleMouseUp);
        this.canvas.addEventListener('mousemove', this.handleMouseMove);
        
        // Touch events
        this.canvas.addEventListener('touchstart', this.handleTouchStart, { passive: false });
        this.canvas.addEventListener('touchend', this.handleTouchEnd, { passive: false });
        this.canvas.addEventListener('touchmove', this.handleTouchMove, { passive: false });
        
        // Prevent context menu on canvas
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // Initialize virtual joystick for touch devices
        if (this.isTouchDevice) {
            this.initVirtualJoystick();
        }
        
        console.log('InputManager initialized');
    }
    
    setupDefaultMappings() {
        // Movement controls
        this.keyMappings.set('moveUp', ['ArrowUp', 'KeyW']);
        this.keyMappings.set('moveDown', ['ArrowDown', 'KeyS']);
        this.keyMappings.set('moveLeft', ['ArrowLeft', 'KeyA']);
        this.keyMappings.set('moveRight', ['ArrowRight', 'KeyD']);
        
        // Action controls
        this.keyMappings.set('fire', ['Space', 'Enter']);
        this.keyMappings.set('pause', ['Escape', 'KeyP']);
        this.keyMappings.set('interact', ['KeyE', 'KeyF']);
        
        // Debug controls
        this.keyMappings.set('debug', ['F1']);
    }
    
    // Keyboard event handlers
    handleKeyDown(event) {
        const key = event.code;
        
        if (!this.keys.get(key)) {
            this.keysPressed.set(key, true);
        }
        
        this.keys.set(key, true);
        
        // Prevent default for game controls
        if (this.isGameKey(key)) {
            event.preventDefault();
        }
    }
    
    handleKeyUp(event) {
        const key = event.code;
        this.keys.set(key, false);
        this.keysReleased.set(key, true);
        
        if (this.isGameKey(key)) {
            event.preventDefault();
        }
    }
    
    // Mouse event handlers
    handleMouseDown(event) {
        const button = event.button;
        
        if (!this.mouseButtons.get(button)) {
            this.mousePressed.set(button, true);
        }
        
        this.mouseButtons.set(button, true);
        this.updateMousePosition(event);
        
        event.preventDefault();
    }
    
    handleMouseUp(event) {
        const button = event.button;
        this.mouseButtons.set(button, false);
        this.mouseReleased.set(button, true);
        this.updateMousePosition(event);
        
        event.preventDefault();
    }
    
    handleMouseMove(event) {
        this.updateMousePosition(event);
    }
    
    updateMousePosition(event) {
        const rect = this.canvas.getBoundingClientRect();
        this.mousePosition.set(
            event.clientX - rect.left,
            event.clientY - rect.top
        );
    }
    
    // Touch event handlers
    handleTouchStart(event) {
        event.preventDefault();
        
        for (const touch of event.changedTouches) {
            const touchPos = this.getTouchPosition(touch);
            this.touches.set(touch.identifier, touchPos);
            this.touchStarted.set(touch.identifier, touchPos.clone());
            
            // Update virtual joystick
            if (this.virtualJoystick) {
                this.virtualJoystick.handleTouchStart(touch.identifier, touchPos);
            }
        }
    }
    
    handleTouchEnd(event) {
        event.preventDefault();
        
        for (const touch of event.changedTouches) {
            const touchPos = this.getTouchPosition(touch);
            this.touchEnded.set(touch.identifier, touchPos);
            this.touches.delete(touch.identifier);
            
            // Update virtual joystick
            if (this.virtualJoystick) {
                this.virtualJoystick.handleTouchEnd(touch.identifier);
            }
        }
    }
    
    handleTouchMove(event) {
        event.preventDefault();
        
        for (const touch of event.changedTouches) {
            const touchPos = this.getTouchPosition(touch);
            this.touches.set(touch.identifier, touchPos);
            
            // Update virtual joystick
            if (this.virtualJoystick) {
                this.virtualJoystick.handleTouchMove(touch.identifier, touchPos);
            }
        }
    }
    
    getTouchPosition(touch) {
        const rect = this.canvas.getBoundingClientRect();
        return new Vector2(
            touch.clientX - rect.left,
            touch.clientY - rect.top
        );
    }
    
    // Input query methods
    isKeyDown(key) {
        return this.keys.get(key) || false;
    }
    
    isKeyPressed(key) {
        return this.keysPressed.get(key) || false;
    }
    
    isKeyReleased(key) {
        return this.keysReleased.get(key) || false;
    }
    
    isMouseDown(button = 0) {
        return this.mouseButtons.get(button) || false;
    }
    
    isMousePressed(button = 0) {
        return this.mousePressed.get(button) || false;
    }
    
    isMouseReleased(button = 0) {
        return this.mouseReleased.get(button) || false;
    }
    
    // Action-based input queries
    isActionDown(action) {
        const keys = this.keyMappings.get(action);
        if (!keys) return false;
        
        return keys.some(key => this.isKeyDown(key));
    }
    
    isActionPressed(action) {
        const keys = this.keyMappings.get(action);
        if (!keys) return false;
        
        return keys.some(key => this.isKeyPressed(key));
    }
    
    isActionReleased(action) {
        const keys = this.keyMappings.get(action);
        if (!keys) return false;
        
        return keys.some(key => this.isKeyReleased(key));
    }
    
    // Movement input helpers
    getMovementVector() {
        const movement = new Vector2(0, 0);
        
        if (this.isActionDown('moveLeft')) movement.x -= 1;
        if (this.isActionDown('moveRight')) movement.x += 1;
        if (this.isActionDown('moveUp')) movement.y -= 1;
        if (this.isActionDown('moveDown')) movement.y += 1;
        
        // Add virtual joystick input for touch devices
        if (this.virtualJoystick && this.virtualJoystick.isActive()) {
            const joystickInput = this.virtualJoystick.getInput();
            movement.addInPlace(joystickInput);
        }
        
        // Normalize diagonal movement
        if (movement.magnitude() > 1) {
            movement.normalizeInPlace();
        }
        
        return movement;
    }
    
    // Key mapping management
    setKeyMapping(action, keys) {
        this.keyMappings.set(action, Array.isArray(keys) ? keys : [keys]);
    }
    
    addKeyMapping(action, key) {
        const existing = this.keyMappings.get(action) || [];
        existing.push(key);
        this.keyMappings.set(action, existing);
    }
    
    removeKeyMapping(action, key) {
        const existing = this.keyMappings.get(action) || [];
        const filtered = existing.filter(k => k !== key);
        this.keyMappings.set(action, filtered);
    }
    
    // Utility methods
    isGameKey(key) {
        for (const keys of this.keyMappings.values()) {
            if (keys.includes(key)) {
                return true;
            }
        }
        return false;
    }
    
    // Virtual joystick for touch devices
    initVirtualJoystick() {
        this.virtualJoystick = new VirtualJoystick(this.canvas);
    }
    
    // Update method - call once per frame
    update() {
        // Clear frame-specific input states
        this.keysPressed.clear();
        this.keysReleased.clear();
        this.mousePressed.clear();
        this.mouseReleased.clear();
        this.touchStarted.clear();
        this.touchEnded.clear();
        
        // Update virtual joystick
        if (this.virtualJoystick) {
            this.virtualJoystick.update();
        }
    }
    
    // Render debug info and virtual controls
    render(ctx) {
        if (this.virtualJoystick) {
            this.virtualJoystick.render(ctx);
        }
    }
    
    // Cleanup
    destroy() {
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        
        this.canvas.removeEventListener('mousedown', this.handleMouseDown);
        this.canvas.removeEventListener('mouseup', this.handleMouseUp);
        this.canvas.removeEventListener('mousemove', this.handleMouseMove);
        
        this.canvas.removeEventListener('touchstart', this.handleTouchStart);
        this.canvas.removeEventListener('touchend', this.handleTouchEnd);
        this.canvas.removeEventListener('touchmove', this.handleTouchMove);
        
        if (this.virtualJoystick) {
            this.virtualJoystick.destroy();
        }
        
        console.log('InputManager destroyed');
    }
}

/**
 * Virtual Joystick for touch devices
 */
class VirtualJoystick {
    constructor(canvas) {
        this.canvas = canvas;
        this.active = false;
        this.touchId = null;
        
        // Joystick properties
        this.center = new Vector2(100, canvas.height - 100);
        this.knobPosition = new Vector2(100, canvas.height - 100);
        this.maxDistance = 50;
        this.deadZone = 0.1;
        
        // Visual properties
        this.baseRadius = 60;
        this.knobRadius = 25;
        this.baseColor = 'rgba(255, 255, 255, 0.3)';
        this.knobColor = 'rgba(255, 255, 255, 0.7)';
    }
    
    handleTouchStart(touchId, position) {
        // Check if touch is within joystick area
        const distance = position.distance(this.center);
        if (distance <= this.baseRadius) {
            this.active = true;
            this.touchId = touchId;
            this.knobPosition.setFromVector(position);
            this.clampKnobPosition();
        }
    }
    
    handleTouchMove(touchId, position) {
        if (this.active && this.touchId === touchId) {
            this.knobPosition.setFromVector(position);
            this.clampKnobPosition();
        }
    }
    
    handleTouchEnd(touchId) {
        if (this.active && this.touchId === touchId) {
            this.active = false;
            this.touchId = null;
            this.knobPosition.setFromVector(this.center);
        }
    }
    
    clampKnobPosition() {
        const offset = this.knobPosition.subtract(this.center);
        if (offset.magnitude() > this.maxDistance) {
            offset.normalizeInPlace().multiplyInPlace(this.maxDistance);
            this.knobPosition = this.center.add(offset);
        }
    }
    
    getInput() {
        if (!this.active) return new Vector2(0, 0);
        
        const offset = this.knobPosition.subtract(this.center);
        const magnitude = offset.magnitude() / this.maxDistance;
        
        if (magnitude < this.deadZone) {
            return new Vector2(0, 0);
        }
        
        return offset.normalize().multiply(magnitude);
    }
    
    isActive() {
        return this.active;
    }
    
    update() {
        // Update joystick position based on screen size changes
        this.center.set(100, this.canvas.height - 100);
        if (!this.active) {
            this.knobPosition.setFromVector(this.center);
        }
    }
    
    render(ctx) {
        // Draw base circle
        ctx.save();
        ctx.fillStyle = this.baseColor;
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
        ctx.lineWidth = 2;
        
        ctx.beginPath();
        ctx.arc(this.center.x, this.center.y, this.baseRadius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        
        // Draw knob
        ctx.fillStyle = this.knobColor;
        ctx.beginPath();
        ctx.arc(this.knobPosition.x, this.knobPosition.y, this.knobRadius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        
        ctx.restore();
    }
    
    destroy() {
        // Cleanup if needed
    }
}