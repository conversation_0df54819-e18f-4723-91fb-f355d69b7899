import { GAME_CONFIG } from '../config/gameConfig.js';

/**
 * PowerUp base class for all power-up types
 * Provides common functionality for power-up effects, duration tracking, and application
 */
export class PowerUp {
    constructor(type, cost, duration = null, description = '') {
        this.type = type;
        this.cost = cost;
        this.duration = duration; // null for permanent power-ups
        this.description = description;
        this.isActive = false;
        this.timeRemaining = duration;
        this.appliedAt = null;
        this.id = PowerUp.generateId();
        
        // Visual properties
        this.icon = null;
        this.color = '#00ffff';
        this.glowColor = '#ffffff';
    }
    
    // Static ID generator
    static idCounter = 0;
    static generateId() {
        return `powerup_${++PowerUp.idCounter}`;
    }
    
    /**
     * Apply the power-up effect to the player ship
     * @param {PlayerShip} playerShip - The player ship to apply the effect to
     * @returns {boolean} True if successfully applied
     */
    apply(playerShip) {
        if (this.isActive) {
            console.warn(`PowerUp ${this.type} is already active`);
            return false;
        }
        
        this.isActive = true;
        this.appliedAt = Date.now();
        this.timeRemaining = this.duration;
        
        console.log(`Applied power-up: ${this.type}`);
        return this.applyEffect(playerShip);
    }
    
    /**
     * Remove the power-up effect from the player ship
     * @param {PlayerShip} playerShip - The player ship to remove the effect from
     * @returns {boolean} True if successfully removed
     */
    remove(playerShip) {
        if (!this.isActive) {
            console.warn(`PowerUp ${this.type} is not active`);
            return false;
        }
        
        this.isActive = false;
        this.timeRemaining = 0;
        
        console.log(`Removed power-up: ${this.type}`);
        return this.removeEffect(playerShip);
    }
    
    /**
     * Update the power-up (handle duration countdown)
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {PlayerShip} playerShip - The player ship (for auto-removal)
     * @returns {boolean} True if power-up is still active
     */
    update(deltaTime, playerShip) {
        if (!this.isActive) return false;
        
        // Handle duration countdown for temporary power-ups
        if (this.duration !== null && this.timeRemaining > 0) {
            this.timeRemaining -= deltaTime;
            
            // Auto-remove when expired
            if (this.timeRemaining <= 0) {
                this.remove(playerShip);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Get the remaining time as a percentage (0-1)
     * @returns {number} Percentage of time remaining
     */
    getTimeRemainingPercentage() {
        if (this.duration === null) return 1; // Permanent power-ups
        if (!this.isActive) return 0;
        return Math.max(0, this.timeRemaining / this.duration);
    }
    
    /**
     * Get formatted time remaining string
     * @returns {string} Formatted time string
     */
    getFormattedTimeRemaining() {
        if (this.duration === null) return 'Permanent';
        if (!this.isActive) return 'Inactive';
        
        const seconds = Math.ceil(this.timeRemaining / 1000);
        return `${seconds}s`;
    }
    
    /**
     * Check if this power-up can be purchased (override in subclasses for special conditions)
     * @param {PlayerShip} playerShip - The player ship
     * @param {number} playerTokens - Current player token balance
     * @returns {object} Purchase availability info
     */
    canPurchase(playerShip, playerTokens) {
        return {
            canPurchase: playerTokens >= this.cost && !this.isActive,
            reason: playerTokens < this.cost ? 'insufficient_tokens' : 
                   this.isActive ? 'already_active' : 'available'
        };
    }
    
    /**
     * Apply the specific effect (override in subclasses)
     * @param {PlayerShip} playerShip - The player ship
     * @returns {boolean} True if successfully applied
     */
    applyEffect(playerShip) {
        // Override in subclasses
        return true;
    }
    
    /**
     * Remove the specific effect (override in subclasses)
     * @param {PlayerShip} playerShip - The player ship
     * @returns {boolean} True if successfully removed
     */
    removeEffect(playerShip) {
        // Override in subclasses
        return true;
    }
    
    /**
     * Get power-up info for UI display
     * @returns {object} Power-up display information
     */
    getDisplayInfo() {
        return {
            id: this.id,
            type: this.type,
            cost: this.cost,
            duration: this.duration,
            description: this.description,
            isActive: this.isActive,
            timeRemaining: this.timeRemaining,
            timeRemainingPercentage: this.getTimeRemainingPercentage(),
            formattedTimeRemaining: this.getFormattedTimeRemaining(),
            icon: this.icon,
            color: this.color,
            glowColor: this.glowColor
        };
    }
}

/**
 * Extra Life power-up - Adds one extra life to the player
 */
export class ExtraLifePowerUp extends PowerUp {
    constructor() {
        super(
            'EXTRA_LIFE',
            GAME_CONFIG.POWER_UP_COSTS.EXTRA_LIFE,
            null, // Permanent effect
            'Gain an extra life to continue your journey'
        );
        
        this.icon = '❤️';
        this.color = '#ff4444';
        this.glowColor = '#ff8888';
    }
    
    applyEffect(playerShip) {
        playerShip.addLives(1);
        return true;
    }
    
    canPurchase(playerShip, playerTokens) {
        // Extra life can always be purchased if player has tokens
        return {
            canPurchase: playerTokens >= this.cost,
            reason: playerTokens < this.cost ? 'insufficient_tokens' : 'available'
        };
    }
}

/**
 * Spread Ammo power-up - Gives the player spread pattern ammunition
 */
export class SpreadAmmoPowerUp extends PowerUp {
    constructor() {
        super(
            'SPREAD_AMMO',
            GAME_CONFIG.POWER_UP_COSTS.SPREAD_AMMO,
            30000, // 30 seconds
            'Fire projectiles in a spread pattern for better coverage'
        );
        
        this.icon = '🔥';
        this.color = '#ffaa00';
        this.glowColor = '#ffdd44';
    }
    
    applyEffect(playerShip) {
        if (playerShip.weaponSystem) {
            playerShip.weaponSystem.enableSpreadPattern(true);
            return true;
        }
        return false;
    }
    
    removeEffect(playerShip) {
        if (playerShip.weaponSystem) {
            playerShip.weaponSystem.enableSpreadPattern(false);
            return true;
        }
        return false;
    }
}

/**
 * Extra Wingman power-up - Adds a wingman ship that provides covering fire
 */
export class ExtraWingmanPowerUp extends PowerUp {
    constructor() {
        super(
            'EXTRA_WINGMAN',
            GAME_CONFIG.POWER_UP_COSTS.EXTRA_WINGMAN,
            45000, // 45 seconds
            'Deploy a wingman ship to provide covering fire'
        );
        
        this.icon = '🚀';
        this.color = '#00ff88';
        this.glowColor = '#44ffaa';
        this.wingmanShip = null;
    }
    
    applyEffect(playerShip) {
        // TODO: Implement wingman ship creation and management
        // This would require a WingmanShip class and integration with the game object manager
        console.log('Wingman power-up applied (implementation pending)');
        return true;
    }
    
    removeEffect(playerShip) {
        // TODO: Remove wingman ship from game
        if (this.wingmanShip) {
            this.wingmanShip.destroy();
            this.wingmanShip = null;
        }
        console.log('Wingman power-up removed (implementation pending)');
        return true;
    }
}

/**
 * PowerUp factory for creating power-up instances
 */
export class PowerUpFactory {
    static createPowerUp(type) {
        switch (type) {
            case 'EXTRA_LIFE':
                return new ExtraLifePowerUp();
            case 'SPREAD_AMMO':
                return new SpreadAmmoPowerUp();
            case 'EXTRA_WINGMAN':
                return new ExtraWingmanPowerUp();
            default:
                throw new Error(`Unknown power-up type: ${type}`);
        }
    }
    
    static getAllPowerUpTypes() {
        return ['EXTRA_LIFE', 'SPREAD_AMMO', 'EXTRA_WINGMAN'];
    }
    
    static createAllPowerUps() {
        return this.getAllPowerUpTypes().map(type => this.createPowerUp(type));
    }
}
