import { Projectile } from '../entities/Projectile.js';
import { Vector2 } from '../utils/Vector2.js';
import { GameMath } from '../utils/GameMath.js';
import { ENEMY_TYPES } from '../config/gameConfig.js';

/**
 * EnemyProjectileSystem manages enemy projectiles and firing patterns
 * Handles different projectile types, patterns, and visual effects
 */
export class EnemyProjectileSystem {
    constructor(canvasWidth = 800, canvasHeight = 600, gameObjectManager = null) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        this.gameObjectManager = gameObjectManager;
        
        // Projectile management
        this.activeProjectiles = [];
        this.projectilePool = [];
        this.maxProjectiles = 100;
        
        // Projectile types configuration
        this.projectileTypes = this.initializeProjectileTypes();
        
        // Visual effects
        this.muzzleFlashes = [];
        this.impactEffects = [];
        
        console.log('EnemyProjectileSystem initialized');
    }
    
    /**
     * Initialize projectile type configurations
     * @returns {object} Projectile type configurations
     */
    initializeProjectileTypes() {
        return {
            [ENEMY_TYPES.AIR]: {
                speed: 200,
                damage: 15,
                color: '#87CEEB',
                trailColor: '#B0E0E6',
                size: { width: 3, height: 8 },
                lifetime: 4000,
                pattern: 'straight',
                sound: 'air_shot'
            },
            [ENEMY_TYPES.WATER]: {
                speed: 150,
                damage: 20,
                color: '#4169E1',
                trailColor: '#00BFFF',
                size: { width: 4, height: 6 },
                lifetime: 5000,
                pattern: 'wave',
                sound: 'water_shot'
            },
            [ENEMY_TYPES.FIRE]: {
                speed: 250,
                damage: 25,
                color: '#FF4500',
                trailColor: '#FFD700',
                size: { width: 5, height: 10 },
                lifetime: 3000,
                pattern: 'straight',
                sound: 'fire_shot'
            },
            [ENEMY_TYPES.EARTH]: {
                speed: 120,
                damage: 30,
                color: '#8B4513',
                trailColor: '#DEB887',
                size: { width: 6, height: 8 },
                lifetime: 6000,
                pattern: 'arc',
                sound: 'earth_shot'
            },
            [ENEMY_TYPES.CRYSTAL]: {
                speed: 180,
                damage: 22,
                color: '#9370DB',
                trailColor: '#DDA0DD',
                size: { width: 4, height: 12 },
                lifetime: 4500,
                pattern: 'homing',
                sound: 'crystal_shot'
            },
            [ENEMY_TYPES.SHADOW]: {
                speed: 300,
                damage: 18,
                color: '#2F2F2F',
                trailColor: '#696969',
                size: { width: 3, height: 6 },
                lifetime: 3500,
                pattern: 'phase',
                sound: 'shadow_shot'
            }
        };
    }
    
    /**
     * Update all active enemy projectiles
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position for homing projectiles
     */
    update(deltaTime, playerPosition = null) {
        // Update active projectiles
        this.updateActiveProjectiles(deltaTime, playerPosition);
        
        // Update visual effects
        this.updateMuzzleFlashes(deltaTime);
        this.updateImpactEffects(deltaTime);
        
        // Clean up destroyed projectiles
        this.cleanupProjectiles();
    }
    
    /**
     * Update all active projectiles
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's position for homing
     */
    updateActiveProjectiles(deltaTime, playerPosition) {
        for (const projectile of this.activeProjectiles) {
            if (projectile.active) {
                // Apply special movement patterns
                this.applyProjectilePattern(projectile, deltaTime, playerPosition);
                
                // Update projectile
                projectile.update(deltaTime);
                
                // Check bounds
                if (this.isProjectileOutOfBounds(projectile)) {
                    projectile.destroy();
                }
            }
        }
    }
    
    /**
     * Apply special movement patterns to projectiles
     * @param {EnemyProjectile} projectile - Projectile to update
     * @param {number} deltaTime - Time elapsed since last update
     * @param {Vector2} playerPosition - Player's position
     */
    applyProjectilePattern(projectile, deltaTime, playerPosition) {
        const dt = deltaTime / 1000;
        
        switch (projectile.pattern) {
            case 'straight':
                // No modification needed - projectile moves straight
                break;
                
            case 'wave':
                this.applyWavePattern(projectile, dt);
                break;
                
            case 'arc':
                this.applyArcPattern(projectile, dt);
                break;
                
            case 'homing':
                this.applyHomingPattern(projectile, dt, playerPosition);
                break;
                
            case 'phase':
                this.applyPhasePattern(projectile, dt);
                break;
        }
    }
    
    /**
     * Apply wave movement pattern
     * @param {EnemyProjectile} projectile - Projectile to update
     * @param {number} dt - Delta time in seconds
     */
    applyWavePattern(projectile, dt) {
        if (!projectile.waveData) {
            projectile.waveData = {
                amplitude: 30,
                frequency: 3,
                baseDirection: projectile.velocity.clone().normalize()
            };
        }
        
        const waveOffset = Math.sin(projectile.age / 1000 * projectile.waveData.frequency) * projectile.waveData.amplitude;
        const perpendicular = projectile.waveData.baseDirection.perpendicular();
        const waveVelocity = perpendicular.multiply(waveOffset * dt);
        
        projectile.velocity.addInPlace(waveVelocity);
        projectile.velocity = projectile.velocity.normalize().multiply(projectile.speed);
    }
    
    /**
     * Apply arc movement pattern (gravity-like)
     * @param {EnemyProjectile} projectile - Projectile to update
     * @param {number} dt - Delta time in seconds
     */
    applyArcPattern(projectile, dt) {
        if (!projectile.arcData) {
            projectile.arcData = {
                gravity: 150 // pixels per second squared
            };
        }
        
        // Apply downward acceleration
        projectile.velocity.y += projectile.arcData.gravity * dt;
    }
    
    /**
     * Apply homing movement pattern
     * @param {EnemyProjectile} projectile - Projectile to update
     * @param {number} dt - Delta time in seconds
     * @param {Vector2} playerPosition - Player's position
     */
    applyHomingPattern(projectile, dt, playerPosition) {
        if (!playerPosition) return;
        
        if (!projectile.homingData) {
            projectile.homingData = {
                turnRate: 2.0, // radians per second
                homingDelay: 500, // milliseconds before homing starts
                maxTurnAngle: Math.PI / 4 // maximum turn angle per frame
            };
        }
        
        // Only start homing after delay
        if (projectile.age < projectile.homingData.homingDelay) return;
        
        const directionToPlayer = playerPosition.subtract(projectile.position).normalize();
        const currentDirection = projectile.velocity.normalize();
        
        // Calculate angle difference
        const targetAngle = directionToPlayer.angle();
        const currentAngle = currentDirection.angle();
        let angleDiff = GameMath.angleDifference(currentAngle, targetAngle);
        
        // Limit turn rate
        const maxTurn = projectile.homingData.turnRate * dt;
        angleDiff = GameMath.clamp(angleDiff, -maxTurn, maxTurn);
        
        // Apply turn
        const newAngle = currentAngle + angleDiff;
        const newDirection = Vector2.fromAngle(newAngle);
        projectile.velocity = newDirection.multiply(projectile.speed);
    }
    
    /**
     * Apply phase movement pattern (teleporting/blinking)
     * @param {EnemyProjectile} projectile - Projectile to update
     * @param {number} dt - Delta time in seconds
     */
    applyPhasePattern(projectile, dt) {
        if (!projectile.phaseData) {
            projectile.phaseData = {
                phaseInterval: 800, // milliseconds between phases
                phaseDistance: 40, // pixels to phase forward
                lastPhaseTime: 0,
                isPhasing: false,
                phaseAlpha: 1.0
            };
        }
        
        const timeSinceLastPhase = projectile.age - projectile.phaseData.lastPhaseTime;
        
        if (timeSinceLastPhase >= projectile.phaseData.phaseInterval && !projectile.phaseData.isPhasing) {
            // Start phase
            projectile.phaseData.isPhasing = true;
            projectile.phaseData.lastPhaseTime = projectile.age;
            
            // Teleport forward
            const direction = projectile.velocity.normalize();
            const phaseOffset = direction.multiply(projectile.phaseData.phaseDistance);
            projectile.position.addInPlace(phaseOffset);
        }
        
        // Handle phase visual effect
        if (projectile.phaseData.isPhasing) {
            const phaseProgress = (projectile.age - projectile.phaseData.lastPhaseTime) / 200; // 200ms phase effect
            if (phaseProgress < 1.0) {
                projectile.phaseData.phaseAlpha = 0.3 + 0.7 * Math.abs(Math.sin(phaseProgress * Math.PI * 4));
            } else {
                projectile.phaseData.isPhasing = false;
                projectile.phaseData.phaseAlpha = 1.0;
            }
        }
    }
    
    /**
     * Fire projectile from enemy
     * @param {Vector2} position - Starting position
     * @param {Vector2} direction - Direction to fire
     * @param {string} enemyType - Type of enemy firing
     * @param {object} options - Additional options
     * @returns {EnemyProjectile|null} Created projectile or null if failed
     */
    fireProjectile(position, direction, enemyType, options = {}) {
        if (this.activeProjectiles.length >= this.maxProjectiles) {
            return null; // Too many projectiles
        }
        
        const projectileConfig = this.projectileTypes[enemyType] || this.projectileTypes[ENEMY_TYPES.AIR];
        
        // Get projectile from pool or create new one
        let projectile = this.getProjectileFromPool();
        if (!projectile) {
            projectile = new EnemyProjectile();
        }
        
        // Configure projectile
        const speed = options.speed || projectileConfig.speed;
        const damage = options.damage || projectileConfig.damage;
        
        projectile.initialize(
            position.clone(),
            direction.normalize(),
            speed,
            enemyType,
            damage,
            projectileConfig
        );
        
        // Add to active projectiles
        this.activeProjectiles.push(projectile);
        
        // Register with game object manager
        if (this.gameObjectManager) {
            this.gameObjectManager.addObject(projectile);
        }
        
        // Create muzzle flash effect
        this.createMuzzleFlash(position, direction, projectileConfig);
        
        console.log(`Enemy ${enemyType} fired projectile at (${position.x}, ${position.y})`);
        return projectile;
    }
    
    /**
     * Fire multiple projectiles in a pattern
     * @param {Vector2} position - Starting position
     * @param {Vector2} baseDirection - Base direction
     * @param {string} enemyType - Type of enemy firing
     * @param {string} pattern - Firing pattern ('spread', 'burst', 'spiral')
     * @param {object} options - Pattern options
     * @returns {Array} Array of created projectiles
     */
    firePattern(position, baseDirection, enemyType, pattern, options = {}) {
        const projectiles = [];
        
        switch (pattern) {
            case 'spread':
                projectiles.push(...this.fireSpreadPattern(position, baseDirection, enemyType, options));
                break;
            case 'burst':
                projectiles.push(...this.fireBurstPattern(position, baseDirection, enemyType, options));
                break;
            case 'spiral':
                projectiles.push(...this.fireSpiralPattern(position, baseDirection, enemyType, options));
                break;
            case 'ring':
                projectiles.push(...this.fireRingPattern(position, enemyType, options));
                break;
        }
        
        return projectiles;
    }
    
    /**
     * Fire spread pattern (multiple projectiles in a cone)
     * @param {Vector2} position - Starting position
     * @param {Vector2} baseDirection - Base direction
     * @param {string} enemyType - Enemy type
     * @param {object} options - Pattern options
     * @returns {Array} Created projectiles
     */
    fireSpreadPattern(position, baseDirection, enemyType, options) {
        const projectiles = [];
        const count = options.count || 3;
        const spreadAngle = options.spreadAngle || Math.PI / 6; // 30 degrees
        
        for (let i = 0; i < count; i++) {
            const angleOffset = (i - (count - 1) / 2) * (spreadAngle / (count - 1));
            const direction = baseDirection.rotate(angleOffset);
            
            const projectile = this.fireProjectile(position, direction, enemyType, options);
            if (projectile) {
                projectiles.push(projectile);
            }
        }
        
        return projectiles;
    }
    
    /**
     * Fire burst pattern (rapid succession)
     * @param {Vector2} position - Starting position
     * @param {Vector2} baseDirection - Base direction
     * @param {string} enemyType - Enemy type
     * @param {object} options - Pattern options
     * @returns {Array} Created projectiles
     */
    fireBurstPattern(position, baseDirection, enemyType, options) {
        const projectiles = [];
        const count = options.count || 3;
        const burstDelay = options.burstDelay || 100; // milliseconds between shots
        
        for (let i = 0; i < count; i++) {
            // For burst, we'll create all projectiles immediately but with slight position offsets
            const offset = baseDirection.multiply(i * -10); // Stagger positions slightly
            const adjustedPosition = position.add(offset);
            
            const projectile = this.fireProjectile(adjustedPosition, baseDirection, enemyType, options);
            if (projectile) {
                projectiles.push(projectile);
            }
        }
        
        return projectiles;
    }
    
    /**
     * Fire spiral pattern
     * @param {Vector2} position - Starting position
     * @param {Vector2} baseDirection - Base direction
     * @param {string} enemyType - Enemy type
     * @param {object} options - Pattern options
     * @returns {Array} Created projectiles
     */
    fireSpiralPattern(position, baseDirection, enemyType, options) {
        const projectiles = [];
        const count = options.count || 6;
        const spiralOffset = options.spiralOffset || 0;
        
        for (let i = 0; i < count; i++) {
            const angle = (i / count) * Math.PI * 2 + spiralOffset;
            const direction = Vector2.fromAngle(angle);
            
            const projectile = this.fireProjectile(position, direction, enemyType, options);
            if (projectile) {
                projectiles.push(projectile);
            }
        }
        
        return projectiles;
    }
    
    /**
     * Fire ring pattern (360 degrees)
     * @param {Vector2} position - Starting position
     * @param {string} enemyType - Enemy type
     * @param {object} options - Pattern options
     * @returns {Array} Created projectiles
     */
    fireRingPattern(position, enemyType, options) {
        const projectiles = [];
        const count = options.count || 8;
        
        for (let i = 0; i < count; i++) {
            const angle = (i / count) * Math.PI * 2;
            const direction = Vector2.fromAngle(angle);
            
            const projectile = this.fireProjectile(position, direction, enemyType, options);
            if (projectile) {
                projectiles.push(projectile);
            }
        }
        
        return projectiles;
    }
    
    /**
     * Create muzzle flash effect
     * @param {Vector2} position - Flash position
     * @param {Vector2} direction - Flash direction
     * @param {object} config - Projectile configuration
     */
    createMuzzleFlash(position, direction, config) {
        const flash = {
            position: position.clone(),
            direction: direction.clone(),
            color: config.color,
            size: 8,
            lifetime: 150, // milliseconds
            age: 0,
            alpha: 1.0
        };
        
        this.muzzleFlashes.push(flash);
    }
    
    /**
     * Create impact effect
     * @param {Vector2} position - Impact position
     * @param {string} enemyType - Type of enemy projectile
     */
    createImpactEffect(position, enemyType) {
        const config = this.projectileTypes[enemyType] || this.projectileTypes[ENEMY_TYPES.AIR];
        
        const effect = {
            position: position.clone(),
            color: config.color,
            trailColor: config.trailColor,
            particles: [],
            lifetime: 300, // milliseconds
            age: 0
        };
        
        // Create particles
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const speed = GameMath.random(50, 150);
            
            effect.particles.push({
                position: position.clone(),
                velocity: Vector2.fromAngle(angle).multiply(speed),
                size: GameMath.random(2, 4),
                alpha: 1.0
            });
        }
        
        this.impactEffects.push(effect);
    }
    
    /**
     * Update muzzle flash effects
     * @param {number} deltaTime - Time elapsed since last update
     */
    updateMuzzleFlashes(deltaTime) {
        for (let i = this.muzzleFlashes.length - 1; i >= 0; i--) {
            const flash = this.muzzleFlashes[i];
            flash.age += deltaTime;
            flash.alpha = 1.0 - (flash.age / flash.lifetime);
            
            if (flash.age >= flash.lifetime) {
                this.muzzleFlashes.splice(i, 1);
            }
        }
    }
    
    /**
     * Update impact effects
     * @param {number} deltaTime - Time elapsed since last update
     */
    updateImpactEffects(deltaTime) {
        const dt = deltaTime / 1000;
        
        for (let i = this.impactEffects.length - 1; i >= 0; i--) {
            const effect = this.impactEffects[i];
            effect.age += deltaTime;
            
            // Update particles
            for (const particle of effect.particles) {
                particle.position.addInPlace(particle.velocity.multiply(dt));
                particle.velocity.multiplyInPlace(0.95); // Friction
                particle.alpha = 1.0 - (effect.age / effect.lifetime);
            }
            
            if (effect.age >= effect.lifetime) {
                this.impactEffects.splice(i, 1);
            }
        }
    }
    
    /**
     * Check if projectile is out of bounds
     * @param {EnemyProjectile} projectile - Projectile to check
     * @returns {boolean} True if out of bounds
     */
    isProjectileOutOfBounds(projectile) {
        const margin = 50;
        return projectile.position.x < -margin ||
               projectile.position.x > this.canvasWidth + margin ||
               projectile.position.y < -margin ||
               projectile.position.y > this.canvasHeight + margin;
    }
    
    /**
     * Clean up destroyed projectiles
     */
    cleanupProjectiles() {
        for (let i = this.activeProjectiles.length - 1; i >= 0; i--) {
            const projectile = this.activeProjectiles[i];
            
            if (projectile.destroyed || !projectile.active) {
                // Return to pool
                this.returnProjectileToPool(projectile);
                this.activeProjectiles.splice(i, 1);
            }
        }
    }
    
    /**
     * Get projectile from object pool
     * @returns {EnemyProjectile|null} Pooled projectile or null
     */
    getProjectileFromPool() {
        return this.projectilePool.length > 0 ? this.projectilePool.pop() : null;
    }
    
    /**
     * Return projectile to object pool
     * @param {EnemyProjectile} projectile - Projectile to return
     */
    returnProjectileToPool(projectile) {
        if (this.projectilePool.length < this.maxProjectiles) {
            projectile.reset();
            this.projectilePool.push(projectile);
        }
        
        // Remove from game object manager
        if (this.gameObjectManager) {
            this.gameObjectManager.removeObject(projectile);
        }
    }
    
    /**
     * Check collisions with player
     * @param {PlayerShip} player - Player ship
     * @returns {Array} Array of colliding projectiles
     */
    checkPlayerCollisions(player) {
        const collisions = [];
        
        if (!player.active || player.isInvulnerable) {
            return collisions;
        }
        
        for (const projectile of this.activeProjectiles) {
            if (projectile.active && projectile.collidesWith(player)) {
                collisions.push(projectile);
            }
        }
        
        return collisions;
    }
    
    /**
     * Handle projectile collision with player
     * @param {EnemyProjectile} projectile - Colliding projectile
     * @param {PlayerShip} player - Player ship
     */
    handlePlayerCollision(projectile, player) {
        // Player takes damage
        const damageResult = player.takeDamage(projectile.damage);
        
        // Create impact effect
        this.createImpactEffect(projectile.position, projectile.enemyType);
        
        // Destroy projectile
        projectile.destroy();
        
        console.log(`Enemy projectile hit player for ${damageResult.damageTaken} damage`);
        
        return damageResult;
    }
    
    /**
     * Render all projectiles and effects
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} interpolation - Interpolation factor
     */
    render(ctx, interpolation = 0) {
        // Render active projectiles
        for (const projectile of this.activeProjectiles) {
            if (projectile.visible) {
                projectile.render(ctx, interpolation);
            }
        }
        
        // Render muzzle flashes
        this.renderMuzzleFlashes(ctx);
        
        // Render impact effects
        this.renderImpactEffects(ctx);
        
        // Render debug info
        if (window.DEBUG_MODE) {
            this.renderDebugInfo(ctx);
        }
    }
    
    /**
     * Render muzzle flash effects
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderMuzzleFlashes(ctx) {
        for (const flash of this.muzzleFlashes) {
            ctx.save();
            ctx.globalAlpha = flash.alpha;
            ctx.fillStyle = flash.color;
            
            ctx.translate(flash.position.x, flash.position.y);
            
            // Draw flash as a small burst
            const size = flash.size * (1 + (1 - flash.alpha));
            ctx.beginPath();
            ctx.arc(0, 0, size, 0, Math.PI * 2);
            ctx.fill();
            
            // Add bright center
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(0, 0, size * 0.5, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        }
    }
    
    /**
     * Render impact effects
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderImpactEffects(ctx) {
        for (const effect of this.impactEffects) {
            ctx.save();
            
            for (const particle of effect.particles) {
                ctx.globalAlpha = particle.alpha;
                ctx.fillStyle = effect.color;
                
                ctx.beginPath();
                ctx.arc(particle.position.x, particle.position.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
            }
            
            ctx.restore();
        }
    }
    
    /**
     * Render debug information
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderDebugInfo(ctx) {
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        
        const debugInfo = [
            `Enemy Projectiles: ${this.activeProjectiles.length}`,
            `Muzzle Flashes: ${this.muzzleFlashes.length}`,
            `Impact Effects: ${this.impactEffects.length}`,
            `Projectile Pool: ${this.projectilePool.length}`
        ];
        
        for (let i = 0; i < debugInfo.length; i++) {
            ctx.fillText(debugInfo[i], this.canvasWidth - 200, 20 + i * 15);
        }
    }
    
    /**
     * Reset the projectile system
     */
    reset() {
        // Clear all projectiles
        for (const projectile of this.activeProjectiles) {
            projectile.destroy();
        }
        this.activeProjectiles = [];
        
        // Clear effects
        this.muzzleFlashes = [];
        this.impactEffects = [];
        
        console.log('EnemyProjectileSystem reset');
    }
    
    /**
     * Update canvas dimensions
     * @param {number} width - New canvas width
     * @param {number} height - New canvas height
     */
    updateCanvasDimensions(width, height) {
        this.canvasWidth = width;
        this.canvasHeight = height;
    }
    
    /**
     * Get system statistics
     * @returns {object} Statistics object
     */
    getStatistics() {
        return {
            activeProjectiles: this.activeProjectiles.length,
            pooledProjectiles: this.projectilePool.length,
            muzzleFlashes: this.muzzleFlashes.length,
            impactEffects: this.impactEffects.length,
            maxProjectiles: this.maxProjectiles
        };
    }
}

/**
 * EnemyProjectile class - extends Projectile with enemy-specific behavior
 */
export class EnemyProjectile extends Projectile {
    constructor() {
        super();
        this.enemyType = ENEMY_TYPES.AIR;
        this.pattern = 'straight';
        this.damage = 15;
    }
    
    /**
     * Initialize enemy projectile
     * @param {Vector2} position - Starting position
     * @param {Vector2} direction - Direction vector
     * @param {number} speed - Projectile speed
     * @param {string} enemyType - Enemy type
     * @param {number} damage - Damage amount
     * @param {object} config - Projectile configuration
     */
    initialize(position, direction, speed, enemyType, damage, config) {
        super.initialize(position, direction, speed, 'enemy', null);
        
        this.enemyType = enemyType;
        this.pattern = config.pattern;
        this.damage = damage;
        this.lifetime = config.lifetime;
        
        // Set visual properties
        this.color = config.color;
        this.trailColor = config.trailColor;
        this.width = config.size.width;
        this.height = config.size.height;
        this.collisionRadius = Math.max(this.width, this.height) / 2;
        
        // Clear pattern-specific data
        this.waveData = null;
        this.arcData = null;
        this.homingData = null;
        this.phaseData = null;
        
        return this;
    }
    
    /**
     * Render enemy projectile with type-specific visuals
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} interpolation - Interpolation factor
     */
    render(ctx, interpolation = 0) {
        if (!this.visible) return;
        
        // Apply phase alpha if applicable
        const originalAlpha = ctx.globalAlpha;
        if (this.phaseData && this.phaseData.phaseAlpha < 1.0) {
            ctx.globalAlpha = this.phaseData.phaseAlpha;
        }
        
        // Call parent render method
        super.render(ctx, interpolation);
        
        // Restore alpha
        ctx.globalAlpha = originalAlpha;
    }
    
    /**
     * Render the main projectile body with enemy-specific styling
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {Vector2} renderPos - Interpolated render position
     */
    renderProjectile(ctx, renderPos) {
        ctx.translate(renderPos.x, renderPos.y);
        
        // Draw based on enemy type
        switch (this.enemyType) {
            case ENEMY_TYPES.AIR:
                this.drawAirProjectile(ctx);
                break;
            case ENEMY_TYPES.WATER:
                this.drawWaterProjectile(ctx);
                break;
            case ENEMY_TYPES.FIRE:
                this.drawFireProjectile(ctx);
                break;
            case ENEMY_TYPES.EARTH:
                this.drawEarthProjectile(ctx);
                break;
            case ENEMY_TYPES.CRYSTAL:
                this.drawCrystalProjectile(ctx);
                break;
            case ENEMY_TYPES.SHADOW:
                this.drawShadowProjectile(ctx);
                break;
            default:
                this.drawDefaultProjectile(ctx);
                break;
        }
    }
    
    /**
     * Draw air-type projectile
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawAirProjectile(ctx) {
        // Streamlined shape
        ctx.fillStyle = this.color;
        ctx.strokeStyle = this.trailColor;
        ctx.lineWidth = 1;
        
        ctx.beginPath();
        ctx.ellipse(0, 0, this.width / 2, this.height / 2, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        
        // Wind lines
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(-this.width / 4, -2);
        ctx.lineTo(this.width / 4, -2);
        ctx.moveTo(-this.width / 4, 2);
        ctx.lineTo(this.width / 4, 2);
        ctx.stroke();
    }
    
    /**
     * Draw water-type projectile
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawWaterProjectile(ctx) {
        // Droplet shape
        ctx.fillStyle = this.color;
        ctx.strokeStyle = this.trailColor;
        ctx.lineWidth = 1;
        
        ctx.beginPath();
        ctx.moveTo(0, -this.height / 2);
        ctx.quadraticCurveTo(this.width / 2, 0, 0, this.height / 2);
        ctx.quadraticCurveTo(-this.width / 2, 0, 0, -this.height / 2);
        ctx.fill();
        ctx.stroke();
        
        // Bubble effect
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(-1, -2, 1, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Draw fire-type projectile
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawFireProjectile(ctx) {
        // Flame shape with flicker
        const flicker = Math.sin(this.age / 100) * 0.2;
        
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.moveTo(0, -this.height / 2 + flicker * 2);
        ctx.quadraticCurveTo(this.width / 2 + flicker, 0, 0, this.height / 2);
        ctx.quadraticCurveTo(-this.width / 2 - flicker, 0, 0, -this.height / 2 + flicker * 2);
        ctx.fill();
        
        // Inner flame
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.ellipse(0, 0, this.width / 3, this.height / 3, 0, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Draw earth-type projectile
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawEarthProjectile(ctx) {
        // Rocky, angular shape
        ctx.fillStyle = this.color;
        ctx.strokeStyle = this.trailColor;
        ctx.lineWidth = 2;
        
        ctx.beginPath();
        ctx.moveTo(0, -this.height / 2);
        ctx.lineTo(this.width / 3, -this.height / 4);
        ctx.lineTo(this.width / 2, this.height / 4);
        ctx.lineTo(0, this.height / 2);
        ctx.lineTo(-this.width / 2, this.height / 4);
        ctx.lineTo(-this.width / 3, -this.height / 4);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
    }
    
    /**
     * Draw crystal-type projectile
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawCrystalProjectile(ctx) {
        // Crystalline shape with glow
        const glow = 0.5 + Math.sin(this.age / 200) * 0.5;
        
        // Outer glow
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.width);
        gradient.addColorStop(0, this.color + '80');
        gradient.addColorStop(1, 'transparent');
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, this.width * glow, 0, Math.PI * 2);
        ctx.fill();
        
        // Crystal shape
        ctx.fillStyle = this.color;
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        
        ctx.beginPath();
        ctx.moveTo(0, -this.height / 2);
        ctx.lineTo(this.width / 4, -this.height / 4);
        ctx.lineTo(this.width / 2, 0);
        ctx.lineTo(this.width / 4, this.height / 4);
        ctx.lineTo(0, this.height / 2);
        ctx.lineTo(-this.width / 4, this.height / 4);
        ctx.lineTo(-this.width / 2, 0);
        ctx.lineTo(-this.width / 4, -this.height / 4);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
    }
    
    /**
     * Draw shadow-type projectile
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawShadowProjectile(ctx) {
        // Dark, wispy shape
        ctx.fillStyle = this.color;
        ctx.globalAlpha = 0.8;
        
        // Main body
        ctx.beginPath();
        ctx.ellipse(0, 0, this.width / 2, this.height / 2, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // Wispy tendrils
        const wispiness = Math.sin(this.age / 150) * 0.3;
        ctx.strokeStyle = this.trailColor;
        ctx.lineWidth = 1;
        
        for (let i = 0; i < 4; i++) {
            const angle = (i / 4) * Math.PI * 2;
            const length = 3 + wispiness * 2;
            const startX = Math.cos(angle) * this.width / 3;
            const startY = Math.sin(angle) * this.height / 3;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(startX + Math.cos(angle) * length, startY + Math.sin(angle) * length);
            ctx.stroke();
        }
        
        ctx.globalAlpha = 1.0;
    }
    
    /**
     * Draw default projectile
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    drawDefaultProjectile(ctx) {
        ctx.fillStyle = this.color;
        ctx.strokeStyle = this.trailColor;
        ctx.lineWidth = 1;
        
        ctx.beginPath();
        ctx.ellipse(0, 0, this.width / 2, this.height / 2, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    }
    
    /**
     * Reset enemy projectile
     */
    reset() {
        super.reset();
        this.enemyType = ENEMY_TYPES.AIR;
        this.pattern = 'straight';
        this.damage = 15;
        this.waveData = null;
        this.arcData = null;
        this.homingData = null;
        this.phaseData = null;
    }
}