// Debug script to test enemy spawning and wave logic
// Run this in browser console to test the fixes

console.log('🔧 Enemy System Debug Script Loaded');

// Function to test enemy spawning logic
function testEnemySpawning() {
    console.log('🧪 Testing Enemy Spawning Logic...');
    
    // Check if game engine exists
    if (typeof window.game === 'undefined' || !window.game.gameEngine) {
        console.error('❌ Game engine not found. Make sure game is running.');
        return;
    }
    
    const gameEngine = window.game.gameEngine;
    const enemyManager = gameEngine.enemyManager;
    
    if (!enemyManager) {
        console.error('❌ Enemy manager not found.');
        return;
    }
    
    console.log('✅ Enemy Manager found:', enemyManager);
    console.log('📊 Current Wave:', enemyManager.currentWave);
    console.log('🎯 Wave In Progress:', enemyManager.waveInProgress);
    console.log('👾 Active Enemies:', enemyManager.activeEnemies.length);
    console.log('📈 Enemies Spawned:', enemyManager.enemiesSpawnedInWave);
    console.log('💀 Enemies Killed:', enemyManager.enemiesKilledInWave);
    console.log('🏃 Enemies Escaped:', enemyManager.enemiesEscapedInWave);
    
    if (enemyManager.waveConfig) {
        console.log('⚙️ Wave Config:', enemyManager.waveConfig);
    }
    
    return {
        enemyManager,
        currentWave: enemyManager.currentWave,
        activeEnemies: enemyManager.activeEnemies.length,
        spawned: enemyManager.enemiesSpawnedInWave,
        killed: enemyManager.enemiesKilledInWave,
        escaped: enemyManager.enemiesEscapedInWave
    };
}

// Function to monitor enemy events
function monitorEnemyEvents() {
    console.log('👀 Starting Enemy Event Monitor...');
    
    // Override console.log to catch enemy messages
    const originalLog = console.log;
    console.log = function(...args) {
        const message = args.join(' ');
        if (message.includes('Enemy') || message.includes('Wave') || message.includes('killed') || message.includes('escaped')) {
            originalLog('🎮 ENEMY EVENT:', ...args);
        } else {
            originalLog(...args);
        }
    };
    
    console.log('✅ Enemy event monitoring active');
}

// Function to force start next wave (for testing)
function forceNextWave() {
    if (typeof window.game === 'undefined' || !window.game.gameEngine) {
        console.error('❌ Game engine not found.');
        return;
    }
    
    const enemyManager = window.game.gameEngine.enemyManager;
    if (!enemyManager) {
        console.error('❌ Enemy manager not found.');
        return;
    }
    
    console.log('🚀 Forcing next wave...');
    enemyManager.waveInProgress = false;
    enemyManager.activeEnemies = [];
    enemyManager.startNextWave();
    
    console.log('✅ Next wave started:', enemyManager.currentWave);
}

// Function to simulate enemy escape
function simulateEnemyEscape() {
    if (typeof window.game === 'undefined' || !window.game.gameEngine) {
        console.error('❌ Game engine not found.');
        return;
    }
    
    const enemyManager = window.game.gameEngine.enemyManager;
    if (!enemyManager || enemyManager.activeEnemies.length === 0) {
        console.error('❌ No active enemies to test escape.');
        return;
    }
    
    console.log('🏃 Simulating enemy escape...');
    const enemy = enemyManager.activeEnemies[0];
    console.log('👾 Enemy before escape:', {
        id: enemy.id,
        destroyed: enemy.destroyed,
        isDestroyed: enemy.isDestroyed,
        active: enemy.active
    });
    
    // Simulate enemy going off-screen
    enemy.position.y = 1000; // Move way off screen
    enemy.checkOffScreen(); // This should call destroy()
    
    console.log('👾 Enemy after escape:', {
        id: enemy.id,
        destroyed: enemy.destroyed,
        isDestroyed: enemy.isDestroyed,
        active: enemy.active
    });
}

// Function to get wave pattern info
function getWavePatternInfo(waveNumber = 1) {
    console.log(`📋 Wave ${waveNumber} Pattern Info:`);
    
    if (typeof window.game === 'undefined' || !window.game.gameEngine) {
        console.error('❌ Game engine not found.');
        return;
    }
    
    const enemyManager = window.game.gameEngine.enemyManager;
    if (!enemyManager) {
        console.error('❌ Enemy manager not found.');
        return;
    }
    
    const patterns = enemyManager.getPredefinedWavePattern(waveNumber, 5);
    console.log(`🎯 Wave ${waveNumber} patterns:`, patterns);
    
    return patterns;
}

// Export functions to global scope for easy testing
window.debugEnemySystem = {
    test: testEnemySpawning,
    monitor: monitorEnemyEvents,
    forceWave: forceNextWave,
    simulateEscape: simulateEnemyEscape,
    getPatterns: getWavePatternInfo
};

console.log('🎮 Debug functions available:');
console.log('  debugEnemySystem.test() - Test current enemy state');
console.log('  debugEnemySystem.monitor() - Monitor enemy events');
console.log('  debugEnemySystem.forceWave() - Force next wave');
console.log('  debugEnemySystem.simulateEscape() - Simulate enemy escape');
console.log('  debugEnemySystem.getPatterns(waveNum) - Get wave patterns');

// Auto-start monitoring
monitorEnemyEvents();

// Test initial state
setTimeout(() => {
    console.log('🔍 Initial system check:');
    testEnemySpawning();
}, 2000);
