{"version": 3, "sources": ["../../gg-game-sdk/src/index.ts"], "sourcesContent": ["/**\n * Event types for Goama tournament platform communication\n */\nexport const EVENT_TYPES = {\n    GG_GET_GAME_DATA: \"GG_GET_GAME_DATA\",\n    GG_UPDATE_GAME_DATA: \"GG_UPDATE_GAME_DATA\",\n    GG_SET_GAME_DATA: \"GG_SET_GAME_DATA\",\n    GG_PAUSED_FROM_GAME: \"GG_PAUSED_FROM_GAME\",\n    GG_PAUSED_FROM_PARENT: \"GG_PAUSED_FROM_PARENT\",\n    GG_QUIT_FROM_PARENT: \"GG_QUIT_FROM_PARENT\",\n    GG_GAME_OVER: \"GG_GAME_OVER\",\n    GG_RESUMED_FROM_GAME: \"GG_RESUMED_FROM_GAME\",\n    GG_RESUMED_FROM_PARENT: \"GG_RESUMED_FROM_PARENT\",\n    GG_GAME_LOAD_FINISHED: \"GG_GAME_LOAD_FINISHED\",\n} as const;\n\n/**\n * Type definitions\n */\nexport interface GameData {\n    [key: string]: any;\n}\n\nexport interface GameOverPayload {\n    score: number;\n}\n\nexport interface GameDataPayload {\n    gameData: GameData;\n}\n\nexport interface UpdateDataPayload {\n    data: GameData;\n}\n\nexport interface MessageEvent {\n    data: {\n        event_type: string;\n        payload?: any;\n    };\n}\n\nexport type EventCallback = () => void;\nexport type GameDataCallback = (data: GameData) => void;\n\n/**\n * GG Game SDK Class\n */\nexport class GGSDK {\n    private static instance: GGSDK;\n    private registeredListeners: string[] = [];\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n * Get the target window for communication\n */\n    private getTargetWindow(): Window | null {\n        // Try window.top first (works in your case)\n        try {\n            if (window.top && window.top !== window) {\n                return window.top;\n            }\n        } catch (e) {\n            console.warn(\"window.top access failed:\", (e as Error).message);\n        }\n\n        // Fallback to window.parent\n        try {\n            if (window.parent && window.parent !== window) {\n                return window.parent;\n            }\n        } catch (e) {\n            console.warn(\"window.parent access failed:\", (e as Error).message);\n        }\n\n        return null;\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): GGSDK {\n        if (!GGSDK.instance) {\n            GGSDK.instance = new GGSDK();\n        }\n        return GGSDK.instance;\n    }\n\n    /**\n     * Check if event listener is registered and add if not\n     */\n    private checkRegisteredListenersAndAdd(eventType: string, callback: (event: MessageEvent) => void): void {\n        if (!this.registeredListeners.includes(eventType)) {\n            window.addEventListener(\"message\", callback);\n            this.registeredListeners.push(eventType);\n        }\n    }\n\n    /**\n     * Register event listener\n     */\n    private registerListener(eventType: string, callback: EventCallback): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        const eventCallback = (e: MessageEvent) => {\n            if (e.data.event_type === eventType) {\n                callback();\n            }\n        };\n\n        this.checkRegisteredListenersAndAdd(eventType, eventCallback);\n    }\n\n    /**\n     * Get game data from parent with fallback to default\n     */\n    public getGameData(defaultData: GameData, callback: GameDataCallback): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        const timeout = setTimeout(() => {\n            callback(defaultData);\n        }, 3000);\n\n        const eventCallback = (event: MessageEvent) => {\n            if (event.data.event_type === EVENT_TYPES.GG_SET_GAME_DATA) {\n                clearTimeout(timeout);\n                callback(event.data.payload.gameData);\n            }\n        };\n\n        this.checkRegisteredListenersAndAdd(EVENT_TYPES.GG_SET_GAME_DATA, eventCallback);\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_GET_GAME_DATA,\n                payload: { defaultData },\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Save game data to parent\n     */\n    public saveGameData(data: GameData): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_UPDATE_GAME_DATA,\n                payload: { data },\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Send game over event with score\n     */\n    public gameOver(score: number): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        console.log(\"sending game over to Goama\", score);\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_GAME_OVER,\n                payload: { score },\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Notify parent that game is paused\n     */\n    public gamePaused(): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_PAUSED_FROM_GAME,\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Notify parent that game is resumed\n     */\n    public gameResumed(): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_RESUMED_FROM_GAME,\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Notify parent that game has finished loading\n     */\n    public gameLoaded(): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_GAME_LOAD_FINISHED,\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Listen for pause events from parent\n     */\n    public listenPaused(callback: EventCallback): void {\n        this.registerListener(EVENT_TYPES.GG_PAUSED_FROM_PARENT, callback);\n    }\n\n    /**\n     * Listen for resume events from parent\n     */\n    public listenResumed(callback: EventCallback): void {\n        this.registerListener(EVENT_TYPES.GG_RESUMED_FROM_PARENT, callback);\n    }\n\n    /**\n     * Listen for quit events from parent\n     */\n    public listenQuit(callback: EventCallback): void {\n        this.registerListener(EVENT_TYPES.GG_QUIT_FROM_PARENT, callback);\n    }\n}\n\n// Create and export default instance\nconst ggSDK = GGSDK.getInstance();\n\n// Make available on window object for browser environments\nif (typeof window !== 'undefined') {\n    (window as any).GGSDK = ggSDK;\n}\n\n// Export individual methods for convenience\nexport const {\n    getGameData,\n    saveGameData,\n    gameOver,\n    gamePaused,\n    gameResumed,\n    gameLoaded,\n    listenPaused,\n    listenResumed,\n    listenQuit,\n} = ggSDK;\n\n// Default export\nexport default ggSDK;"], "mappings": ";AAGa,IAAAA,IAAc,EACvBC,kBAAkB,oBAClBC,qBAAqB,uBACrBC,kBAAkB,oBAClBC,qBAAqB,uBACrBC,uBAAuB,yBACvBC,qBAAqB,uBACrBC,cAAc,gBACdC,sBAAsB,wBACtBC,wBAAwB,0BACxBC,uBAAuB,wBAAA;AAVd,IA6CbC,IAAA,WAAA;AAII,WAAAA,KAAAA;AAFQC,SAAmBC,sBAAa,CAAA;EAIvC;AAmNL,SA9MYF,GAAAG,UAAAC,kBAAR,WAAA;AAEI,QAAA;AACI,UAAIC,OAAOC,OAAOD,OAAOC,QAAQD,OAC7B,QAAOA,OAAOC;IAErB,SAAQC,IAAAA;AACLC,cAAQC,KAAK,6BAA8BF,GAAYG,OAAAA;IAC1D;AAGD,QAAA;AACI,UAAIL,OAAOM,UAAUN,OAAOM,WAAWN,OACnC,QAAOA,OAAOM;IAErB,SAAQJ,IAAAA;AACLC,cAAQC,KAAK,gCAAiCF,GAAYG,OAAAA;IAC7D;AAED,WAAO;EAAA,GAMGV,GAAAY,cAAd,WAAA;AAII,WAHKZ,GAAMa,aACPb,GAAMa,WAAW,IAAIb,OAElBA,GAAMa;EAAAA,GAMTb,GAAAG,UAAAW,iCAAR,SAAuCC,IAAmBC,IAAAA;AACjDf,SAAKC,oBAAoBe,SAASF,EAAAA,MACnCV,OAAOa,iBAAiB,WAAWF,EAAAA,GACnCf,KAAKC,oBAAoBiB,KAAKJ,EAAAA;EAAAA,GAO9Bf,GAAAG,UAAAiB,mBAAR,SAAyBL,IAAmBC,IAAAA;AAExC,QADqBf,KAAKG,gBAAAA,GAC1B;AAWAH,WAAKa,+BAA+BC,IANd,SAACR,IAAAA;AACfA,QAAAA,GAAEc,KAAKC,eAAeP,MACtBC,GAAAA;MAER,CAAA;IANC,MAFGR,SAAQe,MAAM,kDAAA;EAAA,GAgBfvB,GAAAG,UAAAqB,cAAP,SAAmBC,IAAuBT,IAAAA;AACtC,QAAMU,KAAezB,KAAKG,gBAAAA;AAC1B,QAAKsB,IAAL;AAKA,UAAMC,KAAUC,WAAW,WAAA;AACvBZ,QAAAA,GAASS,EAAAA;MACZ,GAAE,GAAA;AASHxB,WAAKa,+BAA+BzB,EAAYG,kBAP1B,SAACqC,IAAAA;AACfA,QAAAA,GAAMR,KAAKC,eAAejC,EAAYG,qBACtCsC,aAAaH,EAAAA,GACbX,GAASa,GAAMR,KAAKU,QAAQC,QAAAA;MAEpC,CAAA,GAIAN,GAAaO,YACT,EACIX,YAAYjC,EAAYC,kBACxByC,SAAS,EAAEN,aAAWA,GAAAA,EAAAA,GAE1B,GAAA;IApBH,MAFGjB,SAAQe,MAAM,kDAAA;EAAA,GA6BfvB,GAAYG,UAAA+B,eAAnB,SAAoBb,IAAAA;AAChB,QAAMK,KAAezB,KAAKG,gBAAAA;AACrBsB,IAAAA,KAKLA,GAAaO,YACT,EACIX,YAAYjC,EAAYE,qBACxBwC,SAAS,EAAEV,MAAIA,GAAAA,EAAAA,GAEnB,GAAA,IATAb,QAAQe,MAAM,kDAAA;EAAA,GAgBfvB,GAAQG,UAAAgC,WAAf,SAAgBC,IAAAA;AACZ,QAAMV,KAAezB,KAAKG,gBAAAA;AACrBsB,IAAAA,MAKLlB,QAAQ6B,IAAI,8BAA8BD,EAAAA,GAC1CV,GAAaO,YACT,EACIX,YAAYjC,EAAYO,cACxBmC,SAAS,EAAEK,OAAKA,GAAAA,EAAAA,GAEpB,GAAA,KAVA5B,QAAQe,MAAM,kDAAA;EAAA,GAiBfvB,GAAAG,UAAAmC,aAAP,WAAA;AACI,QAAMZ,KAAezB,KAAKG,gBAAAA;AACrBsB,IAAAA,KAKLA,GAAaO,YACT,EACIX,YAAYjC,EAAYI,oBAAAA,GAE5B,GAAA,IARAe,QAAQe,MAAM,kDAAA;EAAA,GAefvB,GAAAG,UAAAoC,cAAP,WAAA;AACI,QAAMb,KAAezB,KAAKG,gBAAAA;AACrBsB,IAAAA,KAKLA,GAAaO,YACT,EACIX,YAAYjC,EAAYQ,qBAAAA,GAE5B,GAAA,IARAW,QAAQe,MAAM,kDAAA;EAAA,GAefvB,GAAAG,UAAAqC,aAAP,WAAA;AACI,QAAMd,KAAezB,KAAKG,gBAAAA;AACrBsB,IAAAA,KAKLA,GAAaO,YACT,EACIX,YAAYjC,EAAYU,sBAAAA,GAE5B,GAAA,IARAS,QAAQe,MAAM,kDAAA;EAAA,GAefvB,GAAYG,UAAAsC,eAAnB,SAAoBzB,IAAAA;AAChBf,SAAKmB,iBAAiB/B,EAAYK,uBAAuBsB,EAAAA;EAAAA,GAMtDhB,GAAaG,UAAAuC,gBAApB,SAAqB1B,IAAAA;AACjBf,SAAKmB,iBAAiB/B,EAAYS,wBAAwBkB,EAAAA;EAAAA,GAMvDhB,GAAUG,UAAAwC,aAAjB,SAAkB3B,IAAAA;AACdf,SAAKmB,iBAAiB/B,EAAYM,qBAAqBqB,EAAAA;EAAAA,GAE9DhB;AAAD,EAAA;AAtQa,IAyQP4C,IAAQ5C,EAAMY,YAAAA;AAGE,eAAA,OAAXP,WACNA,OAAeL,QAAQ4C;AAAAA,IAKxBpB,IASAoB,EATWpB;AALaoB,IAMxBV,IAQAU,EAAAA;AAdwBA,IAOxBT,IAOAS,EAAKT;AAdmBS,IAQxBN,IAMAM,EAAKN;AAdmBM,IASxBL,IAKAK,EAAKL;AAdmBK,IAUxBJ,IAIAI,EAJUJ;AAVcI,IAWxBH,IAGAG,EAAAA;AAdwBA,IAYxBF,IAEAE,EAAKF;AAdmBE,IAaxBD,IACAC,EAAKD;", "names": ["EVENT_TYPES", "GG_GET_GAME_DATA", "GG_UPDATE_GAME_DATA", "GG_SET_GAME_DATA", "GG_PAUSED_FROM_GAME", "GG_PAUSED_FROM_PARENT", "GG_QUIT_FROM_PARENT", "GG_GAME_OVER", "GG_RESUMED_FROM_GAME", "GG_RESUMED_FROM_PARENT", "GG_GAME_LOAD_FINISHED", "GGSDK", "this", "registeredListeners", "prototype", "getTargetWindow", "window", "top", "e", "console", "warn", "message", "parent", "getInstance", "instance", "checkRegisteredListenersAndAdd", "eventType", "callback", "includes", "addEventListener", "push", "registerListener", "data", "event_type", "error", "getGameData", "defaultData", "targetWindow", "timeout", "setTimeout", "event", "clearTimeout", "payload", "gameData", "postMessage", "saveGameData", "gameOver", "score", "log", "gamePaused", "gameResumed", "gameLoaded", "listenPaused", "listenResumed", "listenQuit", "ggSDK"]}