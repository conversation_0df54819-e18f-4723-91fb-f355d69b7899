// node_modules/gg-game-sdk/dist/index.esm.js
var e = { GG_GET_GAME_DATA: "GG_GET_GAME_DATA", GG_UPDATE_GAME_DATA: "GG_UPDATE_GAME_DATA", GG_SET_GAME_DATA: "GG_SET_GAME_DATA", GG_PAUSED_FROM_GAME: "GG_PAUSED_FROM_GAME", GG_PAUSED_FROM_PARENT: "GG_PAUSED_FROM_PARENT", GG_QUIT_FROM_PARENT: "GG_QUIT_FROM_PARENT", GG_GAME_OVER: "GG_GAME_OVER", GG_RESUMED_FROM_GAME: "GG_RESUMED_FROM_GAME", GG_RESUMED_FROM_PARENT: "GG_RESUMED_FROM_PARENT", GG_GAME_LOAD_FINISHED: "GG_GAME_LOAD_FINISHED" };
var t = function() {
  function t2() {
    this.registeredListeners = [];
  }
  return t2.prototype.getTargetWindow = function() {
    try {
      if (window.top && window.top !== window) return window.top;
    } catch (e2) {
      console.warn("window.top access failed:", e2.message);
    }
    try {
      if (window.parent && window.parent !== window) return window.parent;
    } catch (e2) {
      console.warn("window.parent access failed:", e2.message);
    }
    return null;
  }, t2.getInstance = function() {
    return t2.instance || (t2.instance = new t2()), t2.instance;
  }, t2.prototype.checkRegisteredListenersAndAdd = function(e2, t3) {
    this.registeredListeners.includes(e2) || (window.addEventListener("message", t3), this.registeredListeners.push(e2));
  }, t2.prototype.registerListener = function(e2, t3) {
    if (this.getTargetWindow()) {
      this.checkRegisteredListenersAndAdd(e2, function(n2) {
        n2.data.event_type === e2 && t3();
      });
    } else console.error("Functions should be called from inside an iframe");
  }, t2.prototype.getGameData = function(t3, n2) {
    var o2 = this.getTargetWindow();
    if (o2) {
      var s2 = setTimeout(function() {
        n2(t3);
      }, 3e3);
      this.checkRegisteredListenersAndAdd(e.GG_SET_GAME_DATA, function(t4) {
        t4.data.event_type === e.GG_SET_GAME_DATA && (clearTimeout(s2), n2(t4.data.payload.gameData));
      }), o2.postMessage({ event_type: e.GG_GET_GAME_DATA, payload: { defaultData: t3 } }, "*");
    } else console.error("Functions should be called from inside an iframe");
  }, t2.prototype.saveGameData = function(t3) {
    var n2 = this.getTargetWindow();
    n2 ? n2.postMessage({ event_type: e.GG_UPDATE_GAME_DATA, payload: { data: t3 } }, "*") : console.error("Functions should be called from inside an iframe");
  }, t2.prototype.gameOver = function(t3) {
    var n2 = this.getTargetWindow();
    n2 ? (console.log("sending game over to Goama", t3), n2.postMessage({ event_type: e.GG_GAME_OVER, payload: { score: t3 } }, "*")) : console.error("Functions should be called from inside an iframe");
  }, t2.prototype.gamePaused = function() {
    var t3 = this.getTargetWindow();
    t3 ? t3.postMessage({ event_type: e.GG_PAUSED_FROM_GAME }, "*") : console.error("Functions should be called from inside an iframe");
  }, t2.prototype.gameResumed = function() {
    var t3 = this.getTargetWindow();
    t3 ? t3.postMessage({ event_type: e.GG_RESUMED_FROM_GAME }, "*") : console.error("Functions should be called from inside an iframe");
  }, t2.prototype.gameLoaded = function() {
    var t3 = this.getTargetWindow();
    t3 ? t3.postMessage({ event_type: e.GG_GAME_LOAD_FINISHED }, "*") : console.error("Functions should be called from inside an iframe");
  }, t2.prototype.listenPaused = function(t3) {
    this.registerListener(e.GG_PAUSED_FROM_PARENT, t3);
  }, t2.prototype.listenResumed = function(t3) {
    this.registerListener(e.GG_RESUMED_FROM_PARENT, t3);
  }, t2.prototype.listenQuit = function(t3) {
    this.registerListener(e.GG_QUIT_FROM_PARENT, t3);
  }, t2;
}();
var n = t.getInstance();
"undefined" != typeof window && (window.GGSDK = n);
var o = n.getGameData;
var s = n.saveGameData;
var i = n.gameOver;
var a = n.gamePaused;
var r = n.gameResumed;
var _ = n.gameLoaded;
var G = n.listenPaused;
var d = n.listenResumed;
var A = n.listenQuit;
export {
  e as EVENT_TYPES,
  t as GGSDK,
  n as default,
  _ as gameLoaded,
  i as gameOver,
  a as gamePaused,
  r as gameResumed,
  o as getGameData,
  G as listenPaused,
  A as listenQuit,
  d as listenResumed,
  s as saveGameData
};
//# sourceMappingURL=gg-game-sdk.js.map
