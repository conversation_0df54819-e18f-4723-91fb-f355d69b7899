{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * Event types for Goama tournament platform communication\n */\nexport const EVENT_TYPES = {\n    GG_GET_GAME_DATA: \"GG_GET_GAME_DATA\",\n    GG_UPDATE_GAME_DATA: \"GG_UPDATE_GAME_DATA\",\n    GG_SET_GAME_DATA: \"GG_SET_GAME_DATA\",\n    GG_PAUSED_FROM_GAME: \"GG_PAUSED_FROM_GAME\",\n    GG_PAUSED_FROM_PARENT: \"GG_PAUSED_FROM_PARENT\",\n    GG_QUIT_FROM_PARENT: \"GG_QUIT_FROM_PARENT\",\n    GG_GAME_OVER: \"GG_GAME_OVER\",\n    GG_RESUMED_FROM_GAME: \"GG_RESUMED_FROM_GAME\",\n    GG_RESUMED_FROM_PARENT: \"GG_RESUMED_FROM_PARENT\",\n    GG_GAME_LOAD_FINISHED: \"GG_GAME_LOAD_FINISHED\",\n} as const;\n\n/**\n * Type definitions\n */\nexport interface GameData {\n    [key: string]: any;\n}\n\nexport interface GameOverPayload {\n    score: number;\n}\n\nexport interface GameDataPayload {\n    gameData: GameData;\n}\n\nexport interface UpdateDataPayload {\n    data: GameData;\n}\n\nexport interface MessageEvent {\n    data: {\n        event_type: string;\n        payload?: any;\n    };\n}\n\nexport type EventCallback = () => void;\nexport type GameDataCallback = (data: GameData) => void;\n\n/**\n * GG Game SDK Class\n */\nexport class GGSDK {\n    private static instance: GGSDK;\n    private registeredListeners: string[] = [];\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n * Get the target window for communication\n */\n    private getTargetWindow(): Window | null {\n        // Try window.top first (works in your case)\n        try {\n            if (window.top && window.top !== window) {\n                return window.top;\n            }\n        } catch (e) {\n            console.warn(\"window.top access failed:\", (e as Error).message);\n        }\n\n        // Fallback to window.parent\n        try {\n            if (window.parent && window.parent !== window) {\n                return window.parent;\n            }\n        } catch (e) {\n            console.warn(\"window.parent access failed:\", (e as Error).message);\n        }\n\n        return null;\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): GGSDK {\n        if (!GGSDK.instance) {\n            GGSDK.instance = new GGSDK();\n        }\n        return GGSDK.instance;\n    }\n\n    /**\n     * Check if event listener is registered and add if not\n     */\n    private checkRegisteredListenersAndAdd(eventType: string, callback: (event: MessageEvent) => void): void {\n        if (!this.registeredListeners.includes(eventType)) {\n            window.addEventListener(\"message\", callback);\n            this.registeredListeners.push(eventType);\n        }\n    }\n\n    /**\n     * Register event listener\n     */\n    private registerListener(eventType: string, callback: EventCallback): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        const eventCallback = (e: MessageEvent) => {\n            if (e.data.event_type === eventType) {\n                callback();\n            }\n        };\n\n        this.checkRegisteredListenersAndAdd(eventType, eventCallback);\n    }\n\n    /**\n     * Get game data from parent with fallback to default\n     */\n    public getGameData(defaultData: GameData, callback: GameDataCallback): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        const timeout = setTimeout(() => {\n            callback(defaultData);\n        }, 3000);\n\n        const eventCallback = (event: MessageEvent) => {\n            if (event.data.event_type === EVENT_TYPES.GG_SET_GAME_DATA) {\n                clearTimeout(timeout);\n                callback(event.data.payload.gameData);\n            }\n        };\n\n        this.checkRegisteredListenersAndAdd(EVENT_TYPES.GG_SET_GAME_DATA, eventCallback);\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_GET_GAME_DATA,\n                payload: { defaultData },\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Save game data to parent\n     */\n    public saveGameData(data: GameData): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_UPDATE_GAME_DATA,\n                payload: { data },\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Send game over event with score\n     */\n    public gameOver(score: number): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        console.log(\"sending game over to Goama\", score);\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_GAME_OVER,\n                payload: { score },\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Notify parent that game is paused\n     */\n    public gamePaused(): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_PAUSED_FROM_GAME,\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Notify parent that game is resumed\n     */\n    public gameResumed(): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_RESUMED_FROM_GAME,\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Notify parent that game has finished loading\n     */\n    public gameLoaded(): void {\n        const targetWindow = this.getTargetWindow();\n        if (!targetWindow) {\n            console.error(\"Functions should be called from inside an iframe\");\n            return;\n        }\n\n        targetWindow.postMessage(\n            {\n                event_type: EVENT_TYPES.GG_GAME_LOAD_FINISHED,\n            },\n            \"*\"\n        );\n    }\n\n    /**\n     * Listen for pause events from parent\n     */\n    public listenPaused(callback: EventCallback): void {\n        this.registerListener(EVENT_TYPES.GG_PAUSED_FROM_PARENT, callback);\n    }\n\n    /**\n     * Listen for resume events from parent\n     */\n    public listenResumed(callback: EventCallback): void {\n        this.registerListener(EVENT_TYPES.GG_RESUMED_FROM_PARENT, callback);\n    }\n\n    /**\n     * Listen for quit events from parent\n     */\n    public listenQuit(callback: EventCallback): void {\n        this.registerListener(EVENT_TYPES.GG_QUIT_FROM_PARENT, callback);\n    }\n}\n\n// Create and export default instance\nconst ggSDK = GGSDK.getInstance();\n\n// Make available on window object for browser environments\nif (typeof window !== 'undefined') {\n    (window as any).GGSDK = ggSDK;\n}\n\n// Export individual methods for convenience\nexport const {\n    getGameData,\n    saveGameData,\n    gameOver,\n    gamePaused,\n    gameResumed,\n    gameLoaded,\n    listenPaused,\n    listenResumed,\n    listenQuit,\n} = ggSDK;\n\n// Default export\nexport default ggSDK;"], "names": ["EVENT_TYPES", "GG_GET_GAME_DATA", "GG_UPDATE_GAME_DATA", "GG_SET_GAME_DATA", "GG_PAUSED_FROM_GAME", "GG_PAUSED_FROM_PARENT", "GG_QUIT_FROM_PARENT", "GG_GAME_OVER", "GG_RESUMED_FROM_GAME", "GG_RESUMED_FROM_PARENT", "GG_GAME_LOAD_FINISHED", "GGSDK", "this", "registeredListeners", "prototype", "getTargetWindow", "window", "top", "e", "console", "warn", "message", "parent", "getInstance", "instance", "checkRegisteredListenersAndAdd", "eventType", "callback", "includes", "addEventListener", "push", "registerListener", "data", "event_type", "error", "getGameData", "defaultData", "targetWindow", "timeout", "setTimeout", "event", "clearTimeout", "payload", "gameData", "postMessage", "saveGameData", "gameOver", "score", "log", "gamePaused", "gameResumed", "gameLoaded", "listenPaused", "listenResumed", "listenQuit", "ggSDK"], "mappings": "oEAGa,IAAAA,EAAc,CACvBC,iBAAkB,mBAClBC,oBAAqB,sBACrBC,iBAAkB,mBAClBC,oBAAqB,sBACrBC,sBAAuB,wBACvBC,oBAAqB,sBACrBC,aAAc,eACdC,qBAAsB,uBACtBC,uBAAwB,yBACxBC,sBAAuB,yBAmC3BC,EAAA,WAII,SAAAA,IAFQC,KAAmBC,oBAAa,EAIvC,CAmNL,OA9MYF,EAAAG,UAAAC,gBAAR,WAEI,IACI,GAAIC,OAAOC,KAAOD,OAAOC,MAAQD,OAC7B,OAAOA,OAAOC,GAErB,CAAC,MAAOC,GACLC,QAAQC,KAAK,4BAA8BF,EAAYG,QAC1D,CAGD,IACI,GAAIL,OAAOM,QAAUN,OAAOM,SAAWN,OACnC,OAAOA,OAAOM,MAErB,CAAC,MAAOJ,GACLC,QAAQC,KAAK,+BAAiCF,EAAYG,QAC7D,CAED,OAAO,MAMGV,EAAAY,YAAd,WAII,OAHKZ,EAAMa,WACPb,EAAMa,SAAW,IAAIb,GAElBA,EAAMa,UAMTb,EAAAG,UAAAW,+BAAR,SAAuCC,EAAmBC,GACjDf,KAAKC,oBAAoBe,SAASF,KACnCV,OAAOa,iBAAiB,UAAWF,GACnCf,KAAKC,oBAAoBiB,KAAKJ,KAO9Bf,EAAAG,UAAAiB,iBAAR,SAAyBL,EAAmBC,GAExC,GADqBf,KAAKG,kBAC1B,CAWAH,KAAKa,+BAA+BC,EANd,SAACR,GACfA,EAAEc,KAAKC,aAAeP,GACtBC,GAER,EANC,MAFGR,QAAQe,MAAM,qDAgBfvB,EAAAG,UAAAqB,YAAP,SAAmBC,EAAuBT,GACtC,IAAMU,EAAezB,KAAKG,kBAC1B,GAAKsB,EAAL,CAKA,IAAMC,EAAUC,WAAW,WACvBZ,EAASS,EACZ,EAAE,KASHxB,KAAKa,+BAA+BzB,EAAYG,iBAP1B,SAACqC,GACfA,EAAMR,KAAKC,aAAejC,EAAYG,mBACtCsC,aAAaH,GACbX,EAASa,EAAMR,KAAKU,QAAQC,UAEpC,GAIAN,EAAaO,YACT,CACIX,WAAYjC,EAAYC,iBACxByC,QAAS,CAAEN,YAAWA,IAE1B,IApBH,MAFGjB,QAAQe,MAAM,qDA6BfvB,EAAYG,UAAA+B,aAAnB,SAAoBb,GAChB,IAAMK,EAAezB,KAAKG,kBACrBsB,EAKLA,EAAaO,YACT,CACIX,WAAYjC,EAAYE,oBACxBwC,QAAS,CAAEV,KAAIA,IAEnB,KATAb,QAAQe,MAAM,qDAgBfvB,EAAQG,UAAAgC,SAAf,SAAgBC,GACZ,IAAMV,EAAezB,KAAKG,kBACrBsB,GAKLlB,QAAQ6B,IAAI,6BAA8BD,GAC1CV,EAAaO,YACT,CACIX,WAAYjC,EAAYO,aACxBmC,QAAS,CAAEK,MAAKA,IAEpB,MAVA5B,QAAQe,MAAM,qDAiBfvB,EAAAG,UAAAmC,WAAP,WACI,IAAMZ,EAAezB,KAAKG,kBACrBsB,EAKLA,EAAaO,YACT,CACIX,WAAYjC,EAAYI,qBAE5B,KARAe,QAAQe,MAAM,qDAefvB,EAAAG,UAAAoC,YAAP,WACI,IAAMb,EAAezB,KAAKG,kBACrBsB,EAKLA,EAAaO,YACT,CACIX,WAAYjC,EAAYQ,sBAE5B,KARAW,QAAQe,MAAM,qDAefvB,EAAAG,UAAAqC,WAAP,WACI,IAAMd,EAAezB,KAAKG,kBACrBsB,EAKLA,EAAaO,YACT,CACIX,WAAYjC,EAAYU,uBAE5B,KARAS,QAAQe,MAAM,qDAefvB,EAAYG,UAAAsC,aAAnB,SAAoBzB,GAChBf,KAAKmB,iBAAiB/B,EAAYK,sBAAuBsB,IAMtDhB,EAAaG,UAAAuC,cAApB,SAAqB1B,GACjBf,KAAKmB,iBAAiB/B,EAAYS,uBAAwBkB,IAMvDhB,EAAUG,UAAAwC,WAAjB,SAAkB3B,GACdf,KAAKmB,iBAAiB/B,EAAYM,oBAAqBqB,IAE9DhB,CAAD,IAGM4C,EAAQ5C,EAAMY,cAGE,oBAAXP,SACNA,OAAeL,MAAQ4C,OAKxBpB,EASAoB,EATWpB,YACXU,EAQAU,eAPAT,EAOAS,EAAKT,SANLG,EAMAM,EAAKN,WALLC,EAKAK,EAAKL,YAJLC,EAIAI,EAJUJ,WACVC,EAGAG,eAFAF,EAEAE,EAAKF,cADLC,EACAC,EAAKD"}