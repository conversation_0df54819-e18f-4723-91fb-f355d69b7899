/**
 * Event types for Goama tournament platform communication
 */
export declare const EVENT_TYPES: {
    readonly GG_GET_GAME_DATA: "GG_GET_GAME_DATA";
    readonly GG_UPDATE_GAME_DATA: "GG_UPDATE_GAME_DATA";
    readonly GG_SET_GAME_DATA: "GG_SET_GAME_DATA";
    readonly GG_PAUSED_FROM_GAME: "GG_PAUSED_FROM_GAME";
    readonly GG_PAUSED_FROM_PARENT: "GG_PAUSED_FROM_PARENT";
    readonly GG_QUIT_FROM_PARENT: "GG_QUIT_FROM_PARENT";
    readonly GG_GAME_OVER: "GG_GAME_OVER";
    readonly GG_RESUMED_FROM_GAME: "GG_RESUMED_FROM_GAME";
    readonly GG_RESUMED_FROM_PARENT: "GG_RESUMED_FROM_PARENT";
    readonly GG_GAME_LOAD_FINISHED: "GG_GAME_LOAD_FINISHED";
};
/**
 * Type definitions
 */
export interface GameData {
    [key: string]: any;
}
export interface GameOverPayload {
    score: number;
}
export interface GameDataPayload {
    gameData: GameData;
}
export interface UpdateDataPayload {
    data: GameData;
}
export interface MessageEvent {
    data: {
        event_type: string;
        payload?: any;
    };
}
export type EventCallback = () => void;
export type GameDataCallback = (data: GameData) => void;
/**
 * GG Game SDK Class
 */
export declare class GGSDK {
    private static instance;
    private registeredListeners;
    private constructor();
    /**
 * Get the target window for communication
 */
    private getTargetWindow;
    /**
     * Get singleton instance
     */
    static getInstance(): GGSDK;
    /**
     * Check if event listener is registered and add if not
     */
    private checkRegisteredListenersAndAdd;
    /**
     * Register event listener
     */
    private registerListener;
    /**
     * Get game data from parent with fallback to default
     */
    getGameData(defaultData: GameData, callback: GameDataCallback): void;
    /**
     * Save game data to parent
     */
    saveGameData(data: GameData): void;
    /**
     * Send game over event with score
     */
    gameOver(score: number): void;
    /**
     * Notify parent that game is paused
     */
    gamePaused(): void;
    /**
     * Notify parent that game is resumed
     */
    gameResumed(): void;
    /**
     * Notify parent that game has finished loading
     */
    gameLoaded(): void;
    /**
     * Listen for pause events from parent
     */
    listenPaused(callback: EventCallback): void;
    /**
     * Listen for resume events from parent
     */
    listenResumed(callback: EventCallback): void;
    /**
     * Listen for quit events from parent
     */
    listenQuit(callback: EventCallback): void;
}
declare const ggSDK: GGSDK;
export declare const getGameData: (defaultData: GameData, callback: GameDataCallback) => void, saveGameData: (data: GameData) => void, gameOver: (score: number) => void, gamePaused: () => void, gameResumed: () => void, gameLoaded: () => void, listenPaused: (callback: EventCallback) => void, listenResumed: (callback: EventCallback) => void, listenQuit: (callback: EventCallback) => void;
export default ggSDK;
