"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e={GG_GET_GAME_DATA:"GG_GET_GAME_DATA",GG_UPDATE_GAME_DATA:"GG_UPDATE_GAME_DATA",GG_SET_GAME_DATA:"GG_SET_GAME_DATA",GG_PAUSED_FROM_GAME:"GG_PAUSED_FROM_GAME",GG_PAUSED_FROM_PARENT:"GG_PAUSED_FROM_PARENT",GG_QUIT_FROM_PARENT:"GG_QUIT_FROM_PARENT",GG_GAME_OVER:"GG_GAME_OVER",GG_RESUMED_FROM_GAME:"GG_RESUMED_FROM_GAME",GG_RESUMED_FROM_PARENT:"GG_RESUMED_FROM_PARENT",GG_GAME_LOAD_FINISHED:"GG_GAME_LOAD_FINISHED"},t=function(){function t(){this.registeredListeners=[]}return t.prototype.getTargetWindow=function(){try{if(window.top&&window.top!==window)return window.top}catch(e){console.warn("window.top access failed:",e.message)}try{if(window.parent&&window.parent!==window)return window.parent}catch(e){console.warn("window.parent access failed:",e.message)}return null},t.getInstance=function(){return t.instance||(t.instance=new t),t.instance},t.prototype.checkRegisteredListenersAndAdd=function(e,t){this.registeredListeners.includes(e)||(window.addEventListener("message",t),this.registeredListeners.push(e))},t.prototype.registerListener=function(e,t){if(this.getTargetWindow()){this.checkRegisteredListenersAndAdd(e,function(n){n.data.event_type===e&&t()})}else console.error("Functions should be called from inside an iframe")},t.prototype.getGameData=function(t,n){var s=this.getTargetWindow();if(s){var o=setTimeout(function(){n(t)},3e3);this.checkRegisteredListenersAndAdd(e.GG_SET_GAME_DATA,function(t){t.data.event_type===e.GG_SET_GAME_DATA&&(clearTimeout(o),n(t.data.payload.gameData))}),s.postMessage({event_type:e.GG_GET_GAME_DATA,payload:{defaultData:t}},"*")}else console.error("Functions should be called from inside an iframe")},t.prototype.saveGameData=function(t){var n=this.getTargetWindow();n?n.postMessage({event_type:e.GG_UPDATE_GAME_DATA,payload:{data:t}},"*"):console.error("Functions should be called from inside an iframe")},t.prototype.gameOver=function(t){var n=this.getTargetWindow();n?(console.log("sending game over to Goama",t),n.postMessage({event_type:e.GG_GAME_OVER,payload:{score:t}},"*")):console.error("Functions should be called from inside an iframe")},t.prototype.gamePaused=function(){var t=this.getTargetWindow();t?t.postMessage({event_type:e.GG_PAUSED_FROM_GAME},"*"):console.error("Functions should be called from inside an iframe")},t.prototype.gameResumed=function(){var t=this.getTargetWindow();t?t.postMessage({event_type:e.GG_RESUMED_FROM_GAME},"*"):console.error("Functions should be called from inside an iframe")},t.prototype.gameLoaded=function(){var t=this.getTargetWindow();t?t.postMessage({event_type:e.GG_GAME_LOAD_FINISHED},"*"):console.error("Functions should be called from inside an iframe")},t.prototype.listenPaused=function(t){this.registerListener(e.GG_PAUSED_FROM_PARENT,t)},t.prototype.listenResumed=function(t){this.registerListener(e.GG_RESUMED_FROM_PARENT,t)},t.prototype.listenQuit=function(t){this.registerListener(e.GG_QUIT_FROM_PARENT,t)},t}(),n=t.getInstance();"undefined"!=typeof window&&(window.GGSDK=n);var s=n.getGameData,o=n.saveGameData,a=n.gameOver,r=n.gamePaused,i=n.gameResumed,_=n.gameLoaded,G=n.listenPaused,d=n.listenResumed,A=n.listenQuit;exports.EVENT_TYPES=e,exports.GGSDK=t,exports.default=n,exports.gameLoaded=_,exports.gameOver=a,exports.gamePaused=r,exports.gameResumed=i,exports.getGameData=s,exports.listenPaused=G,exports.listenQuit=A,exports.listenResumed=d,exports.saveGameData=o;
//# sourceMappingURL=index.js.map
