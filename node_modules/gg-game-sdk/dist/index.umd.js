!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).GGSDK={})}(this,function(e){"use strict";var t={GG_GET_GAME_DATA:"GG_GET_GAME_DATA",GG_UPDATE_GAME_DATA:"GG_UPDATE_GAME_DATA",GG_SET_GAME_DATA:"GG_SET_GAME_DATA",GG_PAUSED_FROM_GAME:"GG_PAUSED_FROM_GAME",GG_PAUSED_FROM_PARENT:"GG_PAUSED_FROM_PARENT",GG_QUIT_FROM_PARENT:"GG_QUIT_FROM_PARENT",GG_GAME_OVER:"GG_GAME_OVER",GG_RESUMED_FROM_GAME:"GG_RESUMED_FROM_GAME",GG_RESUMED_FROM_PARENT:"GG_RESUMED_FROM_PARENT",GG_GAME_LOAD_FINISHED:"GG_GAME_LOAD_FINISHED"},n=function(){function e(){this.registeredListeners=[]}return e.prototype.getTargetWindow=function(){try{if(window.top&&window.top!==window)return window.top}catch(e){console.warn("window.top access failed:",e.message)}try{if(window.parent&&window.parent!==window)return window.parent}catch(e){console.warn("window.parent access failed:",e.message)}return null},e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.prototype.checkRegisteredListenersAndAdd=function(e,t){this.registeredListeners.includes(e)||(window.addEventListener("message",t),this.registeredListeners.push(e))},e.prototype.registerListener=function(e,t){if(this.getTargetWindow()){this.checkRegisteredListenersAndAdd(e,function(n){n.data.event_type===e&&t()})}else console.error("Functions should be called from inside an iframe")},e.prototype.getGameData=function(e,n){var o=this.getTargetWindow();if(o){var s=setTimeout(function(){n(e)},3e3);this.checkRegisteredListenersAndAdd(t.GG_SET_GAME_DATA,function(e){e.data.event_type===t.GG_SET_GAME_DATA&&(clearTimeout(s),n(e.data.payload.gameData))}),o.postMessage({event_type:t.GG_GET_GAME_DATA,payload:{defaultData:e}},"*")}else console.error("Functions should be called from inside an iframe")},e.prototype.saveGameData=function(e){var n=this.getTargetWindow();n?n.postMessage({event_type:t.GG_UPDATE_GAME_DATA,payload:{data:e}},"*"):console.error("Functions should be called from inside an iframe")},e.prototype.gameOver=function(e){var n=this.getTargetWindow();n?(console.log("sending game over to Goama",e),n.postMessage({event_type:t.GG_GAME_OVER,payload:{score:e}},"*")):console.error("Functions should be called from inside an iframe")},e.prototype.gamePaused=function(){var e=this.getTargetWindow();e?e.postMessage({event_type:t.GG_PAUSED_FROM_GAME},"*"):console.error("Functions should be called from inside an iframe")},e.prototype.gameResumed=function(){var e=this.getTargetWindow();e?e.postMessage({event_type:t.GG_RESUMED_FROM_GAME},"*"):console.error("Functions should be called from inside an iframe")},e.prototype.gameLoaded=function(){var e=this.getTargetWindow();e?e.postMessage({event_type:t.GG_GAME_LOAD_FINISHED},"*"):console.error("Functions should be called from inside an iframe")},e.prototype.listenPaused=function(e){this.registerListener(t.GG_PAUSED_FROM_PARENT,e)},e.prototype.listenResumed=function(e){this.registerListener(t.GG_RESUMED_FROM_PARENT,e)},e.prototype.listenQuit=function(e){this.registerListener(t.GG_QUIT_FROM_PARENT,e)},e}(),o=n.getInstance();"undefined"!=typeof window&&(window.GGSDK=o);var s=o.getGameData,i=o.saveGameData,a=o.gameOver,r=o.gamePaused,d=o.gameResumed,_=o.gameLoaded,G=o.listenPaused,c=o.listenResumed,u=o.listenQuit;e.EVENT_TYPES=t,e.GGSDK=n,e.default=o,e.gameLoaded=_,e.gameOver=a,e.gamePaused=r,e.gameResumed=d,e.getGameData=s,e.listenPaused=G,e.listenQuit=u,e.listenResumed=c,e.saveGameData=i,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=index.umd.js.map
