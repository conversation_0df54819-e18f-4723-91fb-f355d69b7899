{"name": "gg-game-sdk", "version": "1.3.0", "description": "JavaScript SDK for Goama tournament platform communication", "publishConfig": {"access": "public"}, "main": "dist/index.js", "module": "dist/index.esm.js", "type": "module", "types": "dist/index.d.ts", "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "rollup -c rollup.config.cjs", "build:watch": "rollup -c rollup.config.cjs -w", "test": "jest --config=jest.config.cjs", "test:watch": "jest --config=jest.config.cjs --watch", "test:coverage": "jest --config=jest.config.cjs --coverage", "lint": "eslint src --ext .js,.ts", "lint:fix": "eslint src --ext .js,.ts --fix", "prepublishOnly": "npm run build", "dev": "rollup -c rollup.config.cjs -w", "release:patch": "npm version patch && git push && git push --tags", "release:minor": "npm version minor && git push && git push --tags", "release:major": "npm version major && git push && git push --tags"}, "keywords": ["goama", "game", "sdk", "tournament", "gaming", "iframe", "postmessage"], "author": "Goama", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Goama/gg-game-sdk.git"}, "bugs": {"url": "https://github.com/Goama/gg-game-sdk/issues"}, "homepage": "https://github.com/Goama/gg-game-sdk#readme", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.29.0", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.2", "@types/jest": "^29.5.0", "@types/node": "^24.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "babel-jest": "^29.7.0", "eslint": "^9.29.0", "globals": "^16.2.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "rollup": "^3.25.0", "ts-jest": "^29.4.0", "typescript": "^5.1.0"}, "engines": {"node": ">=12"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "IE 11"], "dependencies": {"tslib": "^2.8.1"}}