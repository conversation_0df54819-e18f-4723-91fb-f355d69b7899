<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orange SDK Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a2e;
            color: white;
        }
        .test-section {
            background: #1a1a3e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #00ffff;
        }
        button {
            background: #00ffff;
            color: #0a0a2e;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        button:hover {
            background: #00cccc;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #2d5a2d; }
        .error { background: #5a2d2d; }
        .info { background: #2d2d5a; }
        pre {
            background: #000;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Orange SDK Integration Test</h1>
    
    <div class="test-section">
        <h2>SDK Status</h2>
        <div id="sdk-status" class="status info">Initializing...</div>
        <button onclick="initializeSDK()">Initialize SDK</button>
        <button onclick="checkSDKStatus()">Check Status</button>
    </div>
    
    <div class="test-section">
        <h2>Data Operations</h2>
        <button onclick="testSaveData()">Test Save Data</button>
        <button onclick="testLoadData()">Test Load Data</button>
        <button onclick="testSpecialStatus()">Test Special Status</button>
        <div id="data-status" class="status info">Ready for testing</div>
        <pre id="data-output"></pre>
    </div>
    
    <div class="test-section">
        <h2>Game Events</h2>
        <button onclick="testGameLoaded()">Game Loaded</button>
        <button onclick="testGameOver()">Game Over</button>
        <button onclick="testGamePause()">Game Pause</button>
        <button onclick="testGameResume()">Game Resume</button>
        <div id="events-status" class="status info">Ready for testing</div>
    </div>
    
    <div class="test-section">
        <h2>Progress Simulation</h2>
        <button onclick="simulateLevelCompletion()">Simulate Level Completion</button>
        <button onclick="simulateTokenEarning()">Simulate Token Earning</button>
        <button onclick="simulateLoginStreak()">Simulate Login Streak</button>
        <div id="progress-status" class="status info">Ready for simulation</div>
        <pre id="progress-output"></pre>
    </div>

    <script type="module">
        import { OrangeSDKManager } from './src/managers/OrangeSDKManager.js';
        
        let sdkManager = null;
        
        // Make functions global for button onclick
        window.initializeSDK = async function() {
            try {
                sdkManager = new OrangeSDKManager();
                
                // Set up callbacks
                sdkManager.setOnDataSavedCallback((data, reason) => {
                    updateStatus('data-status', `Data saved successfully (${reason})`, 'success');
                    updateOutput('data-output', JSON.stringify(data, null, 2));
                });
                
                sdkManager.setOnSaveErrorCallback((error, reason) => {
                    updateStatus('data-status', `Save failed (${reason}): ${error.message}`, 'error');
                });
                
                sdkManager.setOnSpecialStatusCallback((statusType, value) => {
                    updateStatus('progress-status', `Special status: ${statusType} = ${value}`, 'success');
                });
                
                await sdkManager.initialize();
                updateStatus('sdk-status', 'SDK initialized successfully', 'success');
                
            } catch (error) {
                updateStatus('sdk-status', `SDK initialization failed: ${error.message}`, 'error');
            }
        };
        
        window.checkSDKStatus = function() {
            if (!sdkManager) {
                updateStatus('sdk-status', 'SDK not initialized', 'error');
                return;
            }
            
            const stats = sdkManager.getSaveStatistics();
            const specialStatuses = sdkManager.getSpecialStatuses();
            
            updateStatus('sdk-status', 'SDK Status Retrieved', 'success');
            updateOutput('data-output', JSON.stringify({
                saveStats: stats,
                specialStatuses: specialStatuses
            }, null, 2));
        };
        
        window.testSaveData = async function() {
            if (!sdkManager) {
                updateStatus('data-status', 'SDK not initialized', 'error');
                return;
            }
            
            try {
                // Update some test progress
                sdkManager.updatePlayerProgress({
                    score: 1500,
                    level: 3,
                    tokensEarned: 100,
                    levelCompleted: 3,
                    levelScore: 1500,
                    perfectCompletion: true,
                    enemiesDefeated: 15,
                    completionTime: 45.5
                });
                
                await sdkManager.savePlayerData('manual_test');
                updateStatus('data-status', 'Test data saved successfully', 'success');
                
            } catch (error) {
                updateStatus('data-status', `Save test failed: ${error.message}`, 'error');
            }
        };
        
        window.testLoadData = function() {
            if (!sdkManager) {
                updateStatus('data-status', 'SDK not initialized', 'error');
                return;
            }
            
            const playerData = sdkManager.getPlayerData();
            updateStatus('data-status', 'Player data retrieved', 'success');
            updateOutput('data-output', JSON.stringify(playerData, null, 2));
        };
        
        window.testSpecialStatus = function() {
            if (!sdkManager) {
                updateStatus('data-status', 'SDK not initialized', 'error');
                return;
            }
            
            // Test achievement unlock
            sdkManager.unlockAchievement('test_achievement_001');
            
            // Test tournament status
            sdkManager.setTournamentParticipant(true);
            
            updateStatus('data-status', 'Special status tests triggered', 'success');
        };
        
        window.testGameLoaded = function() {
            updateStatus('events-status', 'Game loaded event sent', 'success');
        };
        
        window.testGameOver = function() {
            updateStatus('events-status', 'Game over event sent with score 2500', 'success');
        };
        
        window.testGamePause = function() {
            if (sdkManager) {
                sdkManager.handleGamePause();
            }
            updateStatus('events-status', 'Game pause event sent', 'success');
        };
        
        window.testGameResume = function() {
            if (sdkManager) {
                sdkManager.handleGameResume();
            }
            updateStatus('events-status', 'Game resume event sent', 'success');
        };
        
        window.simulateLevelCompletion = async function() {
            if (!sdkManager) {
                updateStatus('progress-status', 'SDK not initialized', 'error');
                return;
            }
            
            const completionData = {
                levelNumber: 5,
                completed: true,
                completionTime: 62.3,
                score: { totalScore: 3200, tokenReward: 150 },
                enemiesDefeated: 25,
                perfectCompletion: false,
                bonuses: { speed: 100, accuracy: 50 }
            };
            
            await sdkManager.onLevelCompleted(completionData);
            updateStatus('progress-status', 'Level completion simulated', 'success');
            updateOutput('progress-output', JSON.stringify(completionData, null, 2));
        };
        
        window.simulateTokenEarning = async function() {
            if (!sdkManager) {
                updateStatus('progress-status', 'SDK not initialized', 'error');
                return;
            }
            
            await sdkManager.onTokensChanged({ earned: 75 });
            updateStatus('progress-status', 'Token earning simulated', 'success');
        };
        
        window.simulateLoginStreak = function() {
            if (!sdkManager) {
                updateStatus('progress-status', 'SDK not initialized', 'error');
                return;
            }
            
            // Simulate a 3-day login streak
            sdkManager.playerData.specialStatuses.loginStreak = 3;
            sdkManager.playerData.specialStatuses.lastLoginDate = new Date().toDateString();
            sdkManager.processSpecialStatusBonuses();
            
            updateStatus('progress-status', 'Login streak simulated (3 days)', 'success');
        };
        
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        function updateOutput(elementId, content) {
            document.getElementById(elementId).textContent = content;
        }
        
        // Auto-initialize on page load
        window.addEventListener('load', () => {
            setTimeout(initializeSDK, 1000);
        });
    </script>
</body>
</html>
