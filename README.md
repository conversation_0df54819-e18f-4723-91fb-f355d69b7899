# WarpSpace - Reality Warping Shooter

A vertical scrolling shooter game with AI-powered reality warping mechanics, built with HTML5 Canvas and JavaScript.

## Features

- **Reality Warping**: Transform battlefields using AI-generated environments
- **Token Economy**: WISH tokens drive strategic gameplay decisions
- **Orange ID Integration**: Secure authentication with debug bypass
- **Orange SDK**: Progress tracking and tournament platform compatibility
- **AI Integration**: Fal.ai for image generation, LLM for environment analysis
- **Raffle System**: Competitive motivation through daily/weekly prizes

## Development Setup

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Project Structure

```
src/
├── ai/              # AI service integration
├── config/          # Game configuration
├── engine/          # Core game engine
├── entities/        # Game entities (Player, Enemy, etc.)
├── managers/        # Game subsystem managers
├── systems/         # Game systems (Input, Audio, etc.)
├── utils/           # Utility classes and functions
└── main.js          # Application entry point
```

## Game Architecture

The game follows a modular architecture with clear separation of concerns:

- **GameEngine**: Core game loop and state management
- **Managers**: Handle specific subsystems (Auth, Tokens, AI, etc.)
- **Systems**: Provide foundational services (Input, Rendering, etc.)
- **Entities**: Game objects with behavior and state
- **Utils**: Shared utilities and math functions

## Development

The project uses Vite for fast development and hot module replacement. The game is built with vanilla JavaScript ES6+ modules for maximum compatibility and performance.

### Key Technologies

- HTML5 Canvas for rendering
- JavaScript ES6+ modules
- Vite for build tooling
- Orange ID for authentication
- Orange SDK for data persistence
- Fal.ai for AI image generation
- LLM integration for environment analysis

## License

This project is part of the Orange SDK ecosystem and follows the associated licensing terms.