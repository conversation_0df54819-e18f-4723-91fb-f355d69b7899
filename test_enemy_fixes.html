<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enemy Spawning Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass {
            background-color: #2d5a2d;
            border-left: 4px solid #4caf50;
        }
        .fail {
            background-color: #5a2d2d;
            border-left: 4px solid #f44336;
        }
        .info {
            background-color: #2d3a5a;
            border-left: 4px solid #2196f3;
        }
        button {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        #console-output {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Enemy Spawning and Wave Logic Test</h1>
        
        <div class="test-section">
            <h2>Test Instructions</h2>
            <div class="info">
                <p>This test page helps verify the enemy spawning and wave logic fixes:</p>
                <ul>
                    <li>Open the main game in another tab (http://localhost:3000)</li>
                    <li>Open browser console (F12) to see detailed logs</li>
                    <li>Watch for enemy spawn/escape/kill messages</li>
                    <li>Verify wave progression works correctly</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>Expected Behavior</h2>
            <div class="info">
                <h3>Wave 1: Straight Down Pattern</h3>
                <p>Enemies should spawn in a line formation and move straight down</p>
                
                <h3>Wave 2: Triangle Left-Right Pattern</h3>
                <p>Enemies should spawn in V-formation and move: left 2 spaces → right 3 spaces → left 1 space</p>
                
                <h3>Wave 3: Triangle Zigzag</h3>
                <p>Triangle formation with zigzag movement pattern</p>
                
                <h3>Enemy Tracking</h3>
                <p>Console should show:</p>
                <ul>
                    <li>"Enemy X killed" when enemies are destroyed by player</li>
                    <li>"Enemy X escaped" when enemies go off-screen</li>
                    <li>"Wave X completed" when all enemies are processed</li>
                    <li>Correct wave progress counters</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>Test Actions</h2>
            <button onclick="openGame()">Open Game</button>
            <button onclick="openConsole()">Open Console Instructions</button>
            <button onclick="clearConsole()">Clear Console Output</button>
        </div>

        <div class="test-section">
            <h2>Console Output Monitor</h2>
            <div id="console-output">
                Console output will appear here when available...
                
To test manually:
1. Open http://localhost:3000 in another tab
2. Open browser console (F12)
3. Start the game
4. Watch for these log messages:
   - "Starting wave X"
   - "Enemy X killed/escaped"
   - "Wave X completed"
   - "Wave progress: X/Y"

Expected fixes:
✓ Enemies that go off-screen are counted as "escaped"
✓ Wave completion triggers when all enemies are killed OR escaped
✓ Wave counter increments properly
✓ Predefined movement patterns for each wave
✓ No more infinite wave spawning
            </div>
        </div>

        <div class="test-section">
            <h2>Manual Test Checklist</h2>
            <div id="test-checklist">
                <div class="test-result info">
                    <input type="checkbox" id="test1"> 
                    <label for="test1">Wave 1 spawns enemies in straight line formation</label>
                </div>
                <div class="test-result info">
                    <input type="checkbox" id="test2"> 
                    <label for="test2">Wave 2 spawns V-formation with left-right movement</label>
                </div>
                <div class="test-result info">
                    <input type="checkbox" id="test3"> 
                    <label for="test3">Enemies that escape off-screen are logged as "escaped"</label>
                </div>
                <div class="test-result info">
                    <input type="checkbox" id="test4"> 
                    <label for="test4">Wave completes when all enemies are killed/escaped</label>
                </div>
                <div class="test-result info">
                    <input type="checkbox" id="test5"> 
                    <label for="test5">Next wave starts after previous wave completes</label>
                </div>
                <div class="test-result info">
                    <input type="checkbox" id="test6"> 
                    <label for="test6">Wave counter increments correctly</label>
                </div>
                <div class="test-result info">
                    <input type="checkbox" id="test7"> 
                    <label for="test7">No infinite spawning or stuck wave counter</label>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openGame() {
            window.open('http://localhost:3000', '_blank');
        }

        function openConsole() {
            alert('To open console:\n\n1. Press F12 (or Ctrl+Shift+I)\n2. Click "Console" tab\n3. Look for enemy and wave messages\n4. Filter by "Enemy" or "Wave" if needed');
        }

        function clearConsole() {
            document.getElementById('console-output').textContent = 'Console cleared...\n\nTo test manually:\n1. Open http://localhost:3000 in another tab\n2. Open browser console (F12)\n3. Start the game\n4. Watch for enemy spawn/escape/kill messages';
        }

        // Add some interactivity to checkboxes
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const parent = this.closest('.test-result');
                    if (this.checked) {
                        parent.className = 'test-result pass';
                    } else {
                        parent.className = 'test-result info';
                    }
                });
            });
        });
    </script>
</body>
</html>
