<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orange ID Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a2e;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #00ffff;
            border-radius: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: rgba(0, 255, 0, 0.2); }
        .error { background: rgba(255, 0, 0, 0.2); }
        .warning { background: rgba(255, 255, 0, 0.2); }
        button {
            background: #00ffff;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        #bedrock-login-widget {
            min-height: 100px;
            border: 1px dashed #666;
            padding: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>Orange ID Integration Test</h1>
    
    <div class="test-section">
        <h2>Library Loading Test</h2>
        <div id="library-status"></div>
        <button onclick="checkLibraries()">Check Libraries</button>
    </div>
    
    <div class="test-section">
        <h2>Orange ID Widget Test</h2>
        <div id="bedrock-login-widget"></div>
        <div id="widget-status"></div>
        <button onclick="initializeWidget()">Initialize Widget</button>
    </div>
    
    <div class="test-section">
        <h2>Debug Mode Test</h2>
        <div id="debug-status"></div>
        <button onclick="testDebugMode()">Test Debug Mode</button>
    </div>

    <!-- React libraries for Orange ID -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Bedrock Passport for Orange ID -->
    <script src="https://public-cdn-files.pages.dev/bedrock-passport.umd.js"></script>
    
    <!-- Optional: Tailwind CSS for Orange ID styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <script>
        function checkLibraries() {
            const status = document.getElementById('library-status');
            const libraries = {
                'React': !!window.React,
                'ReactDOM': !!window.ReactDOM,
                'Bedrock': !!window.Bedrock
            };
            
            let html = '';
            for (const [name, loaded] of Object.entries(libraries)) {
                const className = loaded ? 'success' : 'error';
                html += `<div class="status ${className}">${name}: ${loaded ? 'Loaded' : 'Not Loaded'}</div>`;
            }
            status.innerHTML = html;
        }
        
        function initializeWidget() {
            const status = document.getElementById('widget-status');
            
            if (!window.React || !window.ReactDOM || !window.Bedrock) {
                status.innerHTML = '<div class="status error">Required libraries not loaded</div>';
                return;
            }
            
            try {
                const container = document.getElementById('bedrock-login-widget');
                const root = ReactDOM.createRoot(container);
                
                const config = {
                    baseUrl: 'https://api.bedrockpassport.com',
                    authCallbackUrl: window.location.origin,
                    tenantId: 'orange-abc123',
                    subscriptionKey: 'your_API_Key'
                };
                
                root.render(
                    React.createElement(
                        window.Bedrock.BedrockPassportProvider,
                        config,
                        React.createElement(window.Bedrock.LoginPanel, {
                            title: "Test Orange ID",
                            logo: "https://irp.cdn-website.com/e81c109a/dms3rep/multi/orange-web3-logo-v2a-20241018.svg",
                            logoAlt: "Orange Web3",
                            features: {
                                enableWalletConnect: false,
                                enableAppleLogin: true,
                                enableGoogleLogin: true,
                                enableEmailLogin: false,
                            },
                            onLoginSuccess: (user, tokens) => {
                                status.innerHTML = '<div class="status success">Login Success: ' + JSON.stringify(user) + '</div>';
                            },
                            onLoginError: (error) => {
                                status.innerHTML = '<div class="status error">Login Error: ' + error.message + '</div>';
                            }
                        })
                    )
                );
                
                status.innerHTML = '<div class="status success">Widget initialized successfully</div>';
            } catch (error) {
                status.innerHTML = '<div class="status error">Widget initialization failed: ' + error.message + '</div>';
            }
        }
        
        function testDebugMode() {
            const status = document.getElementById('debug-status');
            const isDebug = window.location.hostname === 'localhost' || 
                           window.location.hostname === '127.0.0.1' ||
                           window.location.search.includes('debug=true');
            
            status.innerHTML = `<div class="status ${isDebug ? 'success' : 'warning'}">Debug Mode: ${isDebug ? 'ON' : 'OFF'}</div>`;
        }
        
        // Auto-run tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkLibraries();
                testDebugMode();
            }, 1000);
        });
    </script>
</body>
</html>
